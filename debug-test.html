<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - MBBS Vostrix Components</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .component-test {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            min-height: 200px;
        }
        .component-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0056b3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .debug-log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>MBBS Vostrix Component Debug Test</h1>
        <p>This page tests individual components to identify loading issues.</p>
        
        <div id="debug-log" class="debug-log">Starting debug test...\n</div>
        
        <h2>Component Tests</h2>
        
        <div class="component-test">
            <h3>Campus Gallery Component</h3>
            <div id="campus-gallery-test" class="campus-gallery">
                <div class="component-loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div id="campus-status" class="status">Waiting...</div>
        </div>
        
        <div class="component-test">
            <h3>Advantages Component</h3>
            <div id="advantages-test" class="advantages-container">
                <div class="component-loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div id="advantages-status" class="status">Waiting...</div>
        </div>
        
        <div class="component-test">
            <h3>Apply Button Component</h3>
            <div id="apply-button-test" class="apply-button-container">
                <div class="component-loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div id="apply-button-status" class="status">Waiting...</div>
        </div>
        
        <button onclick="runTests()" style="padding: 10px 20px; background: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Run Component Tests
        </button>
        
        <button onclick="clearLoadingStates()" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
            Force Clear Loading States
        </button>
    </div>

    <script type="module">
        import { createOptimizedCampusGallery } from './js/optimized-campus-gallery.js';
        import { createAdvantages } from './js/advantages.js';
        import { createOptimizedApplyButton } from './js/optimized-apply-button.js';

        const debugLog = document.getElementById('debug-log');
        
        function log(message) {
            debugLog.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function updateStatus(componentId, status, type = 'warning') {
            const statusEl = document.getElementById(componentId + '-status');
            statusEl.textContent = status;
            statusEl.className = 'status ' + type;
        }

        // Simple device capabilities mock
        const mockDeviceCapabilities = {
            capabilities: {
                mobile: window.innerWidth < 768,
                webgl: !!window.WebGLRenderingContext,
                performance: 'high'
            },
            shouldUse3D: () => !!window.WebGLRenderingContext && !mockDeviceCapabilities.capabilities.mobile
        };

        async function testComponent(name, factory, containerId) {
            log(`Testing ${name} component...`);
            updateStatus(containerId.replace('-test', ''), 'Testing...', 'warning');
            
            const container = document.getElementById(containerId);
            if (!container) {
                log(`❌ Container not found: ${containerId}`);
                updateStatus(containerId.replace('-test', ''), 'Container not found', 'error');
                return false;
            }

            try {
                // Remove loading state
                const loadingEl = container.querySelector('.component-loading');
                if (loadingEl) {
                    loadingEl.remove();
                }

                // Test component creation
                const component = await factory(container, mockDeviceCapabilities);
                
                if (component) {
                    log(`✅ ${name} component created successfully`);
                    updateStatus(containerId.replace('-test', ''), 'Success!', 'success');
                    return true;
                } else {
                    log(`❌ ${name} component returned null/undefined`);
                    updateStatus(containerId.replace('-test', ''), 'Component returned null', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ ${name} component failed: ${error.message}`);
                updateStatus(containerId.replace('-test', ''), `Error: ${error.message}`, 'error');
                return false;
            }
        }

        window.runTests = async function() {
            log('Starting component tests...');
            
            const tests = [
                { name: 'Campus Gallery', factory: createOptimizedCampusGallery, container: 'campus-gallery-test' },
                { name: 'Advantages', factory: createAdvantages, container: 'advantages-test' },
                { name: 'Apply Button', factory: createOptimizedApplyButton, container: 'apply-button-test' }
            ];

            for (const test of tests) {
                await testComponent(test.name, test.factory, test.container);
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
            }
            
            log('All tests completed!');
        };

        window.clearLoadingStates = function() {
            log('Force clearing all loading states...');
            const loadingElements = document.querySelectorAll('.component-loading');
            loadingElements.forEach(el => {
                el.remove();
                log('Removed loading element');
            });
            
            // Update all statuses
            updateStatus('campus-gallery', 'Loading cleared', 'warning');
            updateStatus('advantages', 'Loading cleared', 'warning');
            updateStatus('apply-button', 'Loading cleared', 'warning');
        };

        // Auto-run tests after a short delay
        setTimeout(() => {
            log('Auto-running tests in 2 seconds...');
            setTimeout(runTests, 2000);
        }, 1000);

        log('Debug test page loaded successfully');
        log('Device capabilities: ' + JSON.stringify(mockDeviceCapabilities.capabilities));
    </script>
</body>
</html>
