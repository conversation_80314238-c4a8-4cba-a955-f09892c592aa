<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MBBS Vostrix - Loading Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .diagnostic-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ccc;
        }
        .status-card.loading { border-color: #ffc107; background: #fff3cd; }
        .status-card.success { border-color: #28a745; background: #d4edda; }
        .status-card.error { border-color: #dc3545; background: #f8d7da; }
        .status-card.warning { border-color: #fd7e14; background: #fff3cd; }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .component-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0056b3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-primary { background: #0056b3; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #000; }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔍 MBBS Vostrix Loading Diagnostic</h1>
        <p>Real-time monitoring of component loading process</p>
        
        <div class="status-grid">
            <div id="overall-status" class="status-card loading">
                <h3><span class="loading-spinner"></span>Overall Status</h3>
                <p>Initializing diagnostic...</p>
            </div>
            
            <div id="imports-status" class="status-card loading">
                <h3>Module Imports</h3>
                <p>Checking imports...</p>
            </div>
            
            <div id="dom-status" class="status-card loading">
                <h3>DOM Elements</h3>
                <p>Checking containers...</p>
            </div>
            
            <div id="components-status" class="status-card loading">
                <h3>Components</h3>
                <p>Loading components...</p>
            </div>
        </div>
        
        <div class="component-test">
            <h3>Component Loading Tests</h3>
            <div id="component-results"></div>
        </div>
        
        <div class="component-test">
            <h3>Real-time Log</h3>
            <div id="diagnostic-log" class="log-container">Starting diagnostic...\n</div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn-primary" onclick="runDiagnostic()">🔄 Run Full Diagnostic</button>
            <button class="btn-warning" onclick="clearLoadingStates()">🧹 Clear Loading States</button>
            <button class="btn-success" onclick="forceLoadSimple()">⚡ Force Load Simple Components</button>
            <button class="btn-danger" onclick="clearLog()">🗑️ Clear Log</button>
        </div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>

    <script type="module">
        const log = document.getElementById('diagnostic-log');
        const componentResults = document.getElementById('component-results');
        
        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            log.textContent += `${timestamp} ${prefix} ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`${prefix} ${message}`);
        }
        
        function updateStatus(cardId, status, message, type = 'loading') {
            const card = document.getElementById(cardId);
            card.className = `status-card ${type}`;
            card.innerHTML = `<h3>${status}</h3><p>${message}</p>`;
        }
        
        async function checkImports() {
            logMessage('Checking module imports...');
            updateStatus('imports-status', 'Checking Imports', 'Testing module loading...', 'loading');
            
            try {
                // Test Three.js import
                const THREE = await import('three');
                logMessage('Three.js imported successfully', 'success');
                
                // Test simple components import
                const simpleComponents = await import('./js/simple-components.js');
                logMessage('Simple components imported successfully', 'success');
                
                // Test main components
                try {
                    const mainComponents = await import('./js/optimized-main.js');
                    logMessage('Main components imported successfully', 'success');
                } catch (error) {
                    logMessage(`Main components import failed: ${error.message}`, 'warning');
                }
                
                updateStatus('imports-status', '✅ Imports OK', 'All critical imports working', 'success');
                return true;
            } catch (error) {
                logMessage(`Import failed: ${error.message}`, 'error');
                updateStatus('imports-status', '❌ Import Error', error.message, 'error');
                return false;
            }
        }
        
        function checkDOM() {
            logMessage('Checking DOM elements...');
            updateStatus('dom-status', 'Checking DOM', 'Verifying containers...', 'loading');
            
            const containers = [
                'campus-gallery',
                'advantages-container', 
                'apply-button-container',
                'globe-container',
                'info-cards-container'
            ];
            
            const results = containers.map(id => {
                const element = document.getElementById(id);
                const exists = !!element;
                const hasLoading = element?.querySelector('.component-loading');
                
                logMessage(`Container ${id}: ${exists ? 'Found' : 'Missing'}${hasLoading ? ' (has loading)' : ''}`, exists ? 'success' : 'error');
                
                return { id, exists, hasLoading, element };
            });
            
            const foundCount = results.filter(r => r.exists).length;
            const loadingCount = results.filter(r => r.hasLoading).length;
            
            updateStatus('dom-status', `${foundCount}/${containers.length} Found`, `${loadingCount} with loading states`, foundCount === containers.length ? 'success' : 'warning');
            
            return results;
        }
        
        async function testComponents() {
            logMessage('Testing component creation...');
            updateStatus('components-status', 'Testing Components', 'Creating test instances...', 'loading');
            
            try {
                const { createSimpleCampusGallery, createSimpleApplyButton } = await import('./js/simple-components.js');
                const { createAdvantages } = await import('./js/advantages.js');
                
                const testContainer = document.createElement('div');
                testContainer.style.width = '300px';
                testContainer.style.height = '200px';
                
                const mockCapabilities = {
                    capabilities: { mobile: false, webgl: true },
                    shouldUse3D: () => false
                };
                
                // Test simple components
                const tests = [
                    { name: 'Simple Campus Gallery', factory: createSimpleCampusGallery },
                    { name: 'Simple Apply Button', factory: createSimpleApplyButton },
                    { name: 'Advantages', factory: createAdvantages }
                ];
                
                let successCount = 0;
                const results = [];
                
                for (const test of tests) {
                    try {
                        const component = await test.factory(testContainer.cloneNode(), mockCapabilities);
                        if (component) {
                            logMessage(`${test.name}: Working`, 'success');
                            successCount++;
                            results.push(`✅ ${test.name}`);
                        } else {
                            logMessage(`${test.name}: Returned null`, 'warning');
                            results.push(`⚠️ ${test.name} (null)`);
                        }
                    } catch (error) {
                        logMessage(`${test.name}: Error - ${error.message}`, 'error');
                        results.push(`❌ ${test.name} (${error.message})`);
                    }
                }
                
                componentResults.innerHTML = results.join('<br>');
                updateStatus('components-status', `${successCount}/${tests.length} Working`, 'Component tests completed', successCount === tests.length ? 'success' : 'warning');
                
                return successCount === tests.length;
            } catch (error) {
                logMessage(`Component testing failed: ${error.message}`, 'error');
                updateStatus('components-status', '❌ Test Failed', error.message, 'error');
                return false;
            }
        }
        
        window.runDiagnostic = async function() {
            logMessage('=== Starting Full Diagnostic ===');
            updateStatus('overall-status', 'Running Diagnostic', 'Checking all systems...', 'loading');
            
            const importsOK = await checkImports();
            const domResults = checkDOM();
            const componentsOK = await testComponents();
            
            const allGood = importsOK && componentsOK;
            
            if (allGood) {
                updateStatus('overall-status', '✅ All Systems OK', 'Ready to load components', 'success');
                logMessage('=== Diagnostic Complete: All systems operational ===', 'success');
            } else {
                updateStatus('overall-status', '⚠️ Issues Found', 'Check individual systems', 'warning');
                logMessage('=== Diagnostic Complete: Issues detected ===', 'warning');
            }
        };
        
        window.clearLoadingStates = function() {
            logMessage('Clearing all loading states...');
            const loadingElements = document.querySelectorAll('.component-loading');
            loadingElements.forEach((el, index) => {
                logMessage(`Removing loading element ${index + 1}`);
                el.remove();
            });
            logMessage(`Cleared ${loadingElements.length} loading states`, 'success');
        };
        
        window.forceLoadSimple = async function() {
            logMessage('Force loading simple components...');
            
            try {
                const { createSimpleCampusGallery, createSimpleApplyButton, injectSimpleStyles } = await import('./js/simple-components.js');
                const { createAdvantages } = await import('./js/advantages.js');
                
                injectSimpleStyles();
                
                const mockCapabilities = {
                    capabilities: { mobile: false, webgl: true },
                    shouldUse3D: () => false
                };
                
                // Load campus gallery
                const campusContainer = document.getElementById('campus-gallery');
                if (campusContainer) {
                    window.clearLoadingStates();
                    await createSimpleCampusGallery(campusContainer, mockCapabilities);
                    logMessage('Simple campus gallery loaded', 'success');
                }
                
                // Load apply button
                const applyContainer = document.getElementById('apply-button-container');
                if (applyContainer) {
                    window.clearLoadingStates();
                    await createSimpleApplyButton(applyContainer, mockCapabilities);
                    logMessage('Simple apply button loaded', 'success');
                }
                
                // Load advantages
                const advantagesContainer = document.getElementById('advantages-container');
                if (advantagesContainer) {
                    window.clearLoadingStates();
                    await createAdvantages(advantagesContainer, mockCapabilities);
                    logMessage('Advantages component loaded', 'success');
                }
                
                logMessage('All simple components force-loaded successfully!', 'success');
                
            } catch (error) {
                logMessage(`Force load failed: ${error.message}`, 'error');
            }
        };
        
        window.clearLog = function() {
            log.textContent = 'Log cleared.\n';
        };
        
        // Auto-run diagnostic on load
        setTimeout(() => {
            logMessage('Auto-running diagnostic...');
            window.runDiagnostic();
        }, 1000);
        
        // Monitor for stuck loading states
        setInterval(() => {
            const loadingElements = document.querySelectorAll('.component-loading');
            if (loadingElements.length > 0) {
                logMessage(`Warning: ${loadingElements.length} loading states still present`, 'warning');
            }
        }, 5000);
    </script>
</body>
</html>
