<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MBBS Vostrix - Study Medicine at FEFU, Russia | WHO & NMC Recognized</title>
  
  <!-- SEO Meta Tags -->
  <meta name="description" content="Study MBBS at Far Eastern Federal University (FEFU), Russia. WHO & NMC recognized, English medium, affordable fees starting from ₹3.8L/year. Apply now!">
  <meta name="keywords" content="MBBS in Russia, FEFU, medical education, WHO recognized, NMC approved, study abroad, medical university">
  <meta name="author" content="MBBS Vostrix">
  
  <!-- Open Graph -->
  <meta property="og:title" content="MBBS Vostrix - Study Medicine at FEFU, Russia">
  <meta property="og:description" content="English-medium MBBS program at Far Eastern Federal University. WHO & NMC recognized. Affordable fees starting from ₹3.8L/year.">
  <meta property="og:image" content="https://mbbsvostrix.com/assets/og-image.svg">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://mbbsvostrix.com/">
  <meta property="twitter:title" content="MBBS Vostrix - Study Medicine at FEFU, Russia">
  <meta property="twitter:description" content="English-medium MBBS program at Far Eastern Federal University. WHO & NMC recognized. Affordable fees starting from ₹3.8L/year.">
  <meta property="twitter:image" content="https://mbbsvostrix.com/assets/twitter-image.svg">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- PWA Icons -->
  <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
  <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/icon-192.svg">
  <link rel="icon" type="image/svg+xml" sizes="32x32" href="assets/icons/icon-32.svg">
  <link rel="icon" type="image/svg+xml" sizes="16x16" href="assets/icons/icon-16.svg">

  <!-- PWA Manifest (disabled for file:// URLs to avoid CORS warning) -->
  <!-- <link rel="manifest" href="manifest.json"> -->
  <meta name="theme-color" content="#0056b3">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="MBBS Vostrix">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="css/optimized-styles.css">
  <link rel="stylesheet" href="css/responsive.css">
  <link rel="stylesheet" href="css/components.css">
  
  <!-- GSAP for animations -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
  
  <style>
    /* Loading spinner styles */
    .component-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      background: #f8f9fa;
      border-radius: 8px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #0056b3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Debug button */
    .debug-btn {
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 9999;
      padding: 10px;
      background: #dc3545;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <!-- Debug button (shows when ?debug=true) -->
  <button id="debug-btn" class="debug-btn" style="display: none;" onclick="clearAllLoadingStates()">
    Clear Loading States
  </button>

  <header role="banner">
    <nav class="navbar" role="navigation" aria-label="Main navigation">
      <div class="nav-container">
        <div class="nav-brand">
          <h1>MBBS Vostrix</h1>
          <span class="tagline">Your Gateway to Medical Excellence</span>
        </div>
        
        <button class="nav-toggle" aria-label="Toggle navigation" aria-expanded="false">
          <span></span>
          <span></span>
          <span></span>
        </button>
        
        <ul class="nav-menu" role="menubar">
          <li role="none"><a href="#home" role="menuitem">Home</a></li>
          <li role="none"><a href="#about" role="menuitem">About FEFU</a></li>
          <li role="none"><a href="#program" role="menuitem">MBBS Program</a></li>
          <li role="none"><a href="#campus" role="menuitem">Campus Life</a></li>
          <li role="none"><a href="#why-fefu" role="menuitem">Why Choose Us</a></li>
          <li role="none"><a href="#apply" role="menuitem" class="cta-nav">Apply Now</a></li>
        </ul>
      </div>
    </nav>
  </header>

  <main id="main-content">
    <section id="home" class="hero-section" role="banner">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            Study <span class="highlight">MBBS</span> at 
            <span class="university-name">FEFU, Russia</span>
          </h1>
          <p class="hero-subtitle">
            World-class medical education in English at Far Eastern Federal University. 
            WHO & NMC recognized with affordable fees starting from ₹3.8L/year.
          </p>
          <div class="hero-cta">
            <a href="#apply" class="cta-button primary">
              <span>Apply Now</span>
              <i class="fas fa-arrow-right" aria-hidden="true"></i>
            </a>
            <a href="#program" class="cta-button secondary">
              <span>Learn More</span>
              <i class="fas fa-info-circle" aria-hidden="true"></i>
            </a>
          </div>
          <div class="hero-stats">
            <div class="stat">
              <span class="stat-number">6</span>
              <span class="stat-label">Years Program</span>
            </div>
            <div class="stat">
              <span class="stat-number">₹3.8L</span>
              <span class="stat-label">Starting Fee/Year</span>
            </div>
            <div class="stat">
              <span class="stat-number">100%</span>
              <span class="stat-label">English Medium</span>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div id="globe-container" class="globe-container" aria-label="Interactive 3D globe showing FEFU location">
            <div class="component-loading">
              <div class="loading-spinner"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="program" class="section-padding" data-component="info-cards">
      <h2 class="section-title">MBBS Program Details</h2>
      <p class="section-description">Comprehensive information about fees, accommodation, eligibility, and program structure at FEFU.</p>
      <div id="info-cards-container" class="info-cards-container">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </section>

    <section id="campus" class="section-padding" data-component="campus-gallery">
      <h2 class="section-title">Campus Life at FEFU</h2>
      <p class="section-description">Experience world-class education on FEFU's stunning Russky Island campus.</p>
      <div id="campus-gallery" class="campus-gallery">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </section>

    <section id="why-fefu" class="section-padding" data-component="advantages">
      <h2 class="section-title">Why Choose FEFU?</h2>
      <p class="section-description">Discover the compelling advantages that make FEFU the ideal choice for your medical education.</p>
      <div id="advantages-container" class="advantages-container">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </section>

    <section id="apply" class="section-padding" data-component="apply-button">
      <h2 class="section-title">Begin Your Medical Journey</h2>
      <p class="section-description">Take the first step toward your medical career at one of Russia's top universities.</p>
      <div id="apply-button-container" class="apply-button-container">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
      
      <div class="application-form">
        <h3>Quick Enquiry Form</h3>
        <form id="enquiry-form" novalidate>
          <div class="form-group">
            <label for="name">Full Name *</label>
            <input type="text" id="name" name="name" required aria-describedby="name-error">
            <span class="error-message" id="name-error"></span>
          </div>
          
          <div class="form-group">
            <label for="email">Email Address *</label>
            <input type="email" id="email" name="email" required aria-describedby="email-error">
            <span class="error-message" id="email-error"></span>
          </div>
          
          <div class="form-group">
            <label for="phone">Phone Number *</label>
            <input type="tel" id="phone" name="phone" required aria-describedby="phone-error">
            <span class="error-message" id="phone-error"></span>
          </div>
          
          <div class="form-group">
            <label for="qualification">Current Qualification</label>
            <select id="qualification" name="qualification">
              <option value="">Select your qualification</option>
              <option value="12th-appearing">12th Appearing</option>
              <option value="12th-completed">12th Completed</option>
              <option value="graduate">Graduate</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="message">Message (Optional)</label>
            <textarea id="message" name="message" rows="4" placeholder="Tell us about your medical career goals..."></textarea>
          </div>
          
          <button type="submit" class="submit-btn">
            <span>Submit Enquiry</span>
            <i class="fas fa-paper-plane" aria-hidden="true"></i>
          </button>
        </form>
      </div>
    </section>
  </main>

  <footer role="contentinfo">
    <div class="footer-content">
      <div class="footer-section">
        <h3>MBBS Vostrix</h3>
        <p>Your trusted partner for medical education at FEFU, Russia. We guide students through every step of their journey to becoming qualified doctors.</p>
      </div>
      
      <div class="footer-section">
        <h4>Quick Links</h4>
        <ul>
          <li><a href="#about">About FEFU</a></li>
          <li><a href="#program">MBBS Program</a></li>
          <li><a href="#campus">Campus Life</a></li>
          <li><a href="#apply">Apply Now</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h4>Contact Info</h4>
        <div class="contact-info">
          <p><i class="fas fa-phone" aria-hidden="true"></i> +91 XXXXX XXXXX</p>
          <p><i class="fas fa-envelope" aria-hidden="true"></i> <EMAIL></p>
          <p><i class="fas fa-map-marker-alt" aria-hidden="true"></i> India Office Address</p>
        </div>
      </div>
      
      <div class="footer-section">
        <h4>Follow Us</h4>
        <div class="social-links">
          <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
          <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
          <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
          <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>
    </div>
    
    <div class="footer-bottom">
      <p>&copy; 2024 MBBS Vostrix. All rights reserved. | <a href="#privacy">Privacy Policy</a> | <a href="#terms">Terms of Service</a></p>
    </div>
  </footer>

  <!-- No-module components script -->
  <script src="js/no-module-components.js"></script>
  
  <!-- Debug functionality -->
  <script>
    // Show debug button if ?debug=true
    if (window.location.search.includes('debug')) {
      document.getElementById('debug-btn').style.display = 'block';
    }
    
    // Global function to clear loading states
    function clearAllLoadingStates() {
      const loadingElements = document.querySelectorAll('.component-loading');
      console.log('Manually clearing', loadingElements.length, 'loading states');
      loadingElements.forEach((el, index) => {
        console.log(`Removing loading element ${index + 1}`);
        el.style.opacity = '0';
        setTimeout(() => el.remove(), 300);
      });
    }
    
    // Auto-clear loading states after 10 seconds as ultimate fallback
    setTimeout(() => {
      const remainingLoading = document.querySelectorAll('.component-loading');
      if (remainingLoading.length > 0) {
        console.log('Auto-clearing remaining loading states after 10 seconds');
        clearAllLoadingStates();
      }
    }, 10000);
    
    // Simple form handling
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('enquiry-form');
      if (form) {
        form.addEventListener('submit', function(e) {
          e.preventDefault();
          
          // Simple validation
          const name = document.getElementById('name').value.trim();
          const email = document.getElementById('email').value.trim();
          const phone = document.getElementById('phone').value.trim();
          
          if (!name || !email || !phone) {
            alert('Please fill in all required fields.');
            return;
          }
          
          // Simulate form submission
          const submitBtn = form.querySelector('.submit-btn');
          const originalText = submitBtn.innerHTML;
          submitBtn.innerHTML = '<span>Submitting...</span>';
          submitBtn.disabled = true;
          
          setTimeout(() => {
            alert('Thank you! Your enquiry has been submitted. We will contact you soon.');
            form.reset();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
          }, 2000);
        });
      }
    });
  </script>
</body>
</html>
