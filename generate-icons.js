// Simple icon generator for MBBS Vostrix PWA
// This creates basic placeholder icons using Canvas API in Node.js
// Run with: node generate-icons.js

const fs = require('fs');
const path = require('path');

// Create assets/icons directory if it doesn't exist
const iconsDir = path.join(__dirname, 'assets', 'icons');
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
}

// Icon sizes needed for PWA
const iconSizes = [
    { size: 16, name: 'icon-16.png' },
    { size: 32, name: 'icon-32.png' },
    { size: 72, name: 'icon-72.png' },
    { size: 96, name: 'icon-96.png' },
    { size: 128, name: 'icon-128.png' },
    { size: 144, name: 'icon-144.png' },
    { size: 152, name: 'icon-152.png' },
    { size: 192, name: 'icon-192.png' },
    { size: 384, name: 'icon-384.png' },
    { size: 512, name: 'icon-512.png' }
];

const maskableIconSizes = [
    { size: 192, name: 'maskable-icon-192.png' },
    { size: 512, name: 'maskable-icon-512.png' }
];

const shortcutIcons = [
    { size: 96, name: 'apply-icon-96.png', text: 'Apply', color: '#28a745' },
    { size: 96, name: 'program-icon-96.png', text: 'Program', color: '#ffc107' },
    { size: 96, name: 'campus-icon-96.png', text: 'Campus', color: '#17a2b8' }
];

// Generate SVG icon content
function generateSVGIcon(size, text, bgColor = '#0056b3', textColor = '#ffffff', isMaskable = false) {
    const fontSize = Math.max(size / 8, 12);
    const lines = text.split('\n');
    const lineHeight = fontSize * 1.2;
    const totalHeight = lines.length * lineHeight;
    const startY = (size / 2) - (totalHeight / 2) + (fontSize / 2);
    
    let textElements = '';
    lines.forEach((line, index) => {
        const y = startY + (index * lineHeight);
        textElements += `<text x="${size/2}" y="${y}" font-family="Arial, sans-serif" font-size="${fontSize}" font-weight="bold" fill="${textColor}" text-anchor="middle" dominant-baseline="middle">${line}</text>\n`;
    });
    
    if (isMaskable) {
        // Maskable icons need safe zone
        const safeZone = size * 0.8;
        const offset = (size - safeZone) / 2;
        
        return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="${size}" height="${size}" fill="${bgColor}"/>
<rect x="${offset}" y="${offset}" width="${safeZone}" height="${safeZone}" fill="${textColor}"/>
<g fill="${bgColor}">
${textElements}
</g>
</svg>`;
    } else {
        return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="${size}" height="${size}" fill="${bgColor}"/>
${textElements}
</svg>`;
    }
}

// Generate social media images
function generateSocialSVG(width, height, filename) {
    const svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
<defs>
<linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#0056b3;stop-opacity:1" />
<stop offset="100%" style="stop-color:#0041a3;stop-opacity:1" />
</linearGradient>
</defs>
<rect width="${width}" height="${height}" fill="url(#grad1)"/>
<text x="${width/2}" y="${height/2 - 60}" font-family="Arial, sans-serif" font-size="72" font-weight="bold" fill="#ffffff" text-anchor="middle" dominant-baseline="middle">MBBS Vostrix</text>
<text x="${width/2}" y="${height/2}" font-family="Arial, sans-serif" font-size="36" fill="#ffffff" text-anchor="middle" dominant-baseline="middle">Study Medicine at FEFU, Russia</text>
<text x="${width/2}" y="${height/2 + 60}" font-family="Arial, sans-serif" font-size="24" fill="#ffffff" text-anchor="middle" dominant-baseline="middle">WHO &amp; NMC Recognized • English Medium • Affordable Fees</text>
</svg>`;
    
    fs.writeFileSync(path.join(__dirname, 'assets', filename), svg);
    console.log(`Generated ${filename}`);
}

// Generate favicon
function generateFavicon() {
    const svg = generateSVGIcon(32, 'MV');
    fs.writeFileSync(path.join(__dirname, 'assets', 'favicon.svg'), svg);
    console.log('Generated favicon.svg');
}

// Generate all icons
function generateAllIcons() {
    console.log('Generating PWA icons for MBBS Vostrix...');
    
    // Generate regular icons
    iconSizes.forEach(icon => {
        const svg = generateSVGIcon(icon.size, 'MBBS\nVostrix');
        fs.writeFileSync(path.join(iconsDir, icon.name.replace('.png', '.svg')), svg);
        console.log(`Generated ${icon.name} (as SVG)`);
    });
    
    // Generate maskable icons
    maskableIconSizes.forEach(icon => {
        const svg = generateSVGIcon(icon.size, 'MV', '#0056b3', '#ffffff', true);
        fs.writeFileSync(path.join(iconsDir, icon.name.replace('.png', '.svg')), svg);
        console.log(`Generated ${icon.name} (as SVG)`);
    });
    
    // Generate shortcut icons
    shortcutIcons.forEach(icon => {
        const svg = generateSVGIcon(icon.size, icon.text, icon.color);
        fs.writeFileSync(path.join(iconsDir, icon.name.replace('.png', '.svg')), svg);
        console.log(`Generated ${icon.name} (as SVG)`);
    });
    
    // Generate favicon
    generateFavicon();
    
    // Generate social media images
    generateSocialSVG(1200, 630, 'og-image.svg');
    generateSocialSVG(1200, 600, 'twitter-image.svg');
    
    console.log('\nAll icons generated successfully!');
    console.log('Note: These are SVG placeholders. For production, convert to PNG using an image converter.');
    console.log('You can use online tools like https://cloudconvert.com/svg-to-png');
}

// Run the generator
generateAllIcons();

// Create a simple HTML file to view all icons
const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MBBS Vostrix Icons Preview</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; margin: 20px 0; }
        .icon-item { text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .icon-item img { max-width: 64px; max-height: 64px; margin-bottom: 10px; }
        .social-preview { margin: 20px 0; }
        .social-preview img { max-width: 300px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>MBBS Vostrix PWA Icons</h1>
        <p>Generated placeholder icons for the PWA. These are SVG files that should be converted to PNG for production use.</p>
        
        <h2>App Icons</h2>
        <div class="icon-grid">
            ${iconSizes.map(icon => `
                <div class="icon-item">
                    <img src="assets/icons/${icon.name.replace('.png', '.svg')}" alt="${icon.name}">
                    <div>${icon.name}</div>
                    <div>${icon.size}x${icon.size}</div>
                </div>
            `).join('')}
        </div>
        
        <h2>Maskable Icons</h2>
        <div class="icon-grid">
            ${maskableIconSizes.map(icon => `
                <div class="icon-item">
                    <img src="assets/icons/${icon.name.replace('.png', '.svg')}" alt="${icon.name}">
                    <div>${icon.name}</div>
                    <div>${icon.size}x${icon.size}</div>
                </div>
            `).join('')}
        </div>
        
        <h2>Shortcut Icons</h2>
        <div class="icon-grid">
            ${shortcutIcons.map(icon => `
                <div class="icon-item">
                    <img src="assets/icons/${icon.name.replace('.png', '.svg')}" alt="${icon.name}">
                    <div>${icon.name}</div>
                    <div>${icon.size}x${icon.size}</div>
                </div>
            `).join('')}
        </div>
        
        <h2>Social Media Images</h2>
        <div class="social-preview">
            <h3>Open Graph Image (1200x630)</h3>
            <img src="assets/og-image.svg" alt="OG Image">
        </div>
        <div class="social-preview">
            <h3>Twitter Image (1200x600)</h3>
            <img src="assets/twitter-image.svg" alt="Twitter Image">
        </div>
        
        <h2>Favicon</h2>
        <div class="icon-item" style="display: inline-block;">
            <img src="assets/favicon.svg" alt="Favicon">
            <div>favicon.svg</div>
            <div>32x32</div>
        </div>
    </div>
</body>
</html>`;

fs.writeFileSync(path.join(__dirname, 'icon-preview.html'), htmlContent);
console.log('Created icon-preview.html to view all generated icons');
