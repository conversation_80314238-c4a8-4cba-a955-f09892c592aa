<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Components - MBBS Vostrix</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .component-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0056b3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .fallback-content {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 8px;
            color: #666;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.loading { background: #fff3cd; color: #856404; }
    </style>
    
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <h1>Simple Components Test</h1>
        <p>Testing simplified, reliable components for MBBS Vostrix</p>
        
        <div id="status" class="status loading">Initializing components...</div>
        
        <div class="test-section">
            <h2>Campus Gallery Component</h2>
            <div id="campus-gallery" class="campus-gallery">
                <div class="component-loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Advantages Component</h2>
            <div id="advantages-container" class="advantages-container">
                <div class="component-loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Apply Button Component</h2>
            <div id="apply-button-container" class="apply-button-container">
                <div class="component-loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Application Form (Target)</h2>
            <div class="application-form" style="padding: 2rem; background: #e9ecef; border-radius: 8px; text-align: center;">
                <h3>Application Form</h3>
                <p>This is where the apply button should scroll to.</p>
                <input type="text" placeholder="Your Name" style="padding: 0.5rem; margin: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
                <input type="email" placeholder="Your Email" style="padding: 0.5rem; margin: 0.5rem; border: 1px solid #ccc; border-radius: 4px;">
            </div>
        </div>
        
        <button onclick="testComponents()" style="padding: 1rem 2rem; background: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 1rem 0;">
            Manually Test Components
        </button>
        
        <button onclick="clearLoadingStates()" style="padding: 1rem 2rem; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 1rem;">
            Force Clear Loading States
        </button>
    </div>

    <script type="module">
        import SimpleMBBSVostrixApp from './js/simple-main.js';
        
        const statusEl = document.getElementById('status');
        
        function updateStatus(message, type = 'loading') {
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
            console.log(message);
        }
        
        // Monitor app initialization
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            checkCount++;
            
            if (window.simpleMbbsVostrixApp && window.simpleMbbsVostrixApp.isLoaded) {
                updateStatus('✅ All components loaded successfully!', 'success');
                clearInterval(checkInterval);
            } else if (checkCount > 20) { // 10 seconds
                updateStatus('❌ Components failed to load within 10 seconds', 'error');
                clearInterval(checkInterval);
                window.clearLoadingStates();
            } else {
                updateStatus(`Loading components... (${checkCount}/20)`, 'loading');
            }
        }, 500);
        
        // Global functions for manual testing
        window.testComponents = function() {
            updateStatus('🔄 Manually testing components...', 'loading');
            
            // Check if components exist
            const campusGallery = document.querySelector('#campus-gallery .simple-gallery');
            const advantages = document.querySelector('#advantages-container .advantage-item');
            const applyButton = document.querySelector('#apply-button-container .simple-apply-button');
            
            let successCount = 0;
            if (campusGallery) successCount++;
            if (advantages) successCount++;
            if (applyButton) successCount++;
            
            if (successCount === 3) {
                updateStatus('✅ All 3 components are working!', 'success');
            } else {
                updateStatus(`⚠️ Only ${successCount}/3 components are working`, 'error');
            }
        };
        
        window.clearLoadingStates = function() {
            updateStatus('🧹 Clearing all loading states...', 'loading');
            
            const loadingElements = document.querySelectorAll('.component-loading');
            loadingElements.forEach((el, index) => {
                console.log(`Removing loading element ${index + 1}`);
                el.remove();
            });
            
            updateStatus(`Cleared ${loadingElements.length} loading states`, 'success');
        };
        
        // Auto-clear loading states after 8 seconds as fallback
        setTimeout(() => {
            const remainingLoading = document.querySelectorAll('.component-loading');
            if (remainingLoading.length > 0) {
                console.log('Auto-clearing remaining loading states');
                window.clearLoadingStates();
            }
        }, 8000);
    </script>
</body>
</html>
