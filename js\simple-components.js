// Simplified, guaranteed-to-work components for MBBS Vostrix
// These components prioritize reliability over advanced features

export function createSimpleCampusGallery(container, deviceCapabilities) {
  console.log('Creating simple campus gallery...');
  
  const images = [
    {
      url: 'https://images.unsplash.com/photo-**********-701939374585?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'FEFU Main Campus',
      description: 'Modern campus facilities on Russky Island'
    },
    {
      url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'Medical Faculty Building',
      description: 'State-of-the-art medical education facilities'
    },
    {
      url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'Student Dormitories',
      description: 'Comfortable accommodation for international students'
    },
    {
      url: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'Laboratory Facilities',
      description: 'Advanced medical research and training labs'
    }
  ];

  // Clear container and create simple gallery
  container.innerHTML = `
    <div class="simple-gallery">
      <div class="gallery-header">
        <h3>Campus Gallery</h3>
        <p>Explore FEFU's modern facilities</p>
      </div>
      <div class="gallery-grid">
        ${images.map((img, index) => `
          <div class="gallery-item" data-index="${index}">
            <img src="${img.url}" alt="${img.title}" loading="lazy">
            <div class="gallery-item-info">
              <h4>${img.title}</h4>
              <p>${img.description}</p>
            </div>
          </div>
        `).join('')}
      </div>
    </div>
  `;

  // Add click handlers
  const galleryItems = container.querySelectorAll('.gallery-item');
  galleryItems.forEach((item, index) => {
    item.addEventListener('click', () => {
      showSimpleModal(images[index]);
    });
    
    // Add hover effects
    item.addEventListener('mouseenter', () => {
      item.style.transform = 'translateY(-5px)';
      item.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
    });
    
    item.addEventListener('mouseleave', () => {
      item.style.transform = 'translateY(0)';
      item.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
    });
  });

  console.log('✅ Simple campus gallery created successfully');
  return {
    type: 'simple',
    container,
    images,
    destroy: () => {
      console.log('Destroying simple campus gallery');
    }
  };
}

export function createSimpleApplyButton(container, deviceCapabilities) {
  console.log('Creating simple apply button...');
  
  container.innerHTML = `
    <div class="simple-apply-button">
      <div class="apply-content">
        <div class="apply-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <h3>Ready to Start Your Medical Journey?</h3>
        <p>Join thousands of students at FEFU's world-class medical program</p>
        <button class="apply-btn" id="main-apply-btn">
          <span>Apply Now</span>
          <i class="fas fa-arrow-right"></i>
        </button>
        <div class="apply-features">
          <div class="feature">
            <i class="fas fa-check"></i>
            <span>WHO & NMC Recognized</span>
          </div>
          <div class="feature">
            <i class="fas fa-check"></i>
            <span>English Medium</span>
          </div>
          <div class="feature">
            <i class="fas fa-check"></i>
            <span>Affordable Fees</span>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add button functionality
  const applyBtn = container.querySelector('#main-apply-btn');
  if (applyBtn) {
    applyBtn.addEventListener('click', () => {
      // Add click animation
      applyBtn.style.transform = 'scale(0.95)';
      setTimeout(() => {
        applyBtn.style.transform = 'scale(1)';
      }, 150);
      
      // Scroll to form
      const form = document.querySelector('.application-form');
      if (form) {
        form.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Focus first input after scroll
        setTimeout(() => {
          const firstInput = form.querySelector('input');
          if (firstInput) firstInput.focus();
        }, 1000);
      }
      
      // Track click
      console.log('Apply button clicked');
      if (typeof gtag !== 'undefined') {
        gtag('event', 'apply_button_click', {
          'event_category': 'conversion',
          'event_label': 'simple_apply_button'
        });
      }
    });

    // Add hover effects
    applyBtn.addEventListener('mouseenter', () => {
      applyBtn.style.transform = 'translateY(-2px)';
      applyBtn.style.boxShadow = '0 8px 25px rgba(0, 86, 179, 0.3)';
    });
    
    applyBtn.addEventListener('mouseleave', () => {
      applyBtn.style.transform = 'translateY(0)';
      applyBtn.style.boxShadow = '0 4px 15px rgba(0, 86, 179, 0.2)';
    });
  }

  console.log('✅ Simple apply button created successfully');
  return {
    type: 'simple',
    container,
    destroy: () => {
      console.log('Destroying simple apply button');
    }
  };
}

function showSimpleModal(imageData) {
  const modal = document.createElement('div');
  modal.className = 'simple-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <button class="modal-close" aria-label="Close">&times;</button>
      <img src="${imageData.url}" alt="${imageData.title}">
      <div class="modal-info">
        <h3>${imageData.title}</h3>
        <p>${imageData.description}</p>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Close handlers
  const closeBtn = modal.querySelector('.modal-close');
  closeBtn.addEventListener('click', () => modal.remove());
  modal.addEventListener('click', (e) => {
    if (e.target === modal) modal.remove();
  });
  
  // Keyboard close
  const handleKeydown = (e) => {
    if (e.key === 'Escape') {
      modal.remove();
      document.removeEventListener('keydown', handleKeydown);
    }
  };
  document.addEventListener('keydown', handleKeydown);

  // Focus management
  closeBtn.focus();
}

// CSS styles for simple components
export function injectSimpleStyles() {
  if (document.getElementById('simple-components-styles')) return;
  
  const styles = document.createElement('style');
  styles.id = 'simple-components-styles';
  styles.textContent = `
    .simple-gallery {
      padding: 2rem;
    }
    
    .gallery-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .gallery-header h3 {
      font-size: 2rem;
      color: #0056b3;
      margin-bottom: 0.5rem;
    }
    
    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }
    
    .gallery-item {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      cursor: pointer;
    }
    
    .gallery-item img {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }
    
    .gallery-item-info {
      padding: 1rem;
    }
    
    .gallery-item-info h4 {
      color: #0056b3;
      margin-bottom: 0.5rem;
    }
    
    .simple-apply-button {
      background: linear-gradient(135deg, #0056b3, #004494);
      border-radius: 12px;
      padding: 3rem 2rem;
      text-align: center;
      color: white;
    }
    
    .apply-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #FFD700;
    }
    
    .apply-content h3 {
      font-size: 2rem;
      margin-bottom: 1rem;
    }
    
    .apply-content p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }
    
    .apply-btn {
      background: #FFD700;
      color: #0056b3;
      border: none;
      padding: 1rem 2rem;
      border-radius: 8px;
      font-size: 1.2rem;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 2rem;
    }
    
    .apply-features {
      display: flex;
      justify-content: center;
      gap: 2rem;
      flex-wrap: wrap;
    }
    
    .feature {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
    }
    
    .feature i {
      color: #28a745;
    }
    
    .simple-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    
    .simple-modal .modal-content {
      background: white;
      border-radius: 8px;
      max-width: 90vw;
      max-height: 90vh;
      position: relative;
      overflow: hidden;
    }
    
    .simple-modal img {
      width: 100%;
      max-height: 60vh;
      object-fit: cover;
    }
    
    .simple-modal .modal-info {
      padding: 1.5rem;
    }
    
    .modal-close {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: rgba(0,0,0,0.5);
      color: white;
      border: none;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      font-size: 1.5rem;
      cursor: pointer;
      z-index: 1001;
    }
    
    @media (max-width: 768px) {
      .gallery-grid {
        grid-template-columns: 1fr;
      }
      
      .apply-features {
        flex-direction: column;
        gap: 1rem;
      }
      
      .simple-apply-button {
        padding: 2rem 1rem;
      }
    }
  `;
  
  document.head.appendChild(styles);
}
