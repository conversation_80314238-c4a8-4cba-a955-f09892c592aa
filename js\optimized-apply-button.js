import * as THREE from 'three';

// Optimized apply button with performance enhancements and GSAP fallback
export function createOptimizedApplyButton(container, deviceCapabilities) {
  // Check device capabilities and WebGL support
  if (!deviceCapabilities.shouldUse3D()) {
    return createFallbackButton(container);
  }

  try {
    return create3DButton(container, deviceCapabilities);
  } catch (error) {
    console.warn('3D button failed, using fallback:', error);
    return createFallbackButton(container);
  }
}

function create3DButton(container, deviceCapabilities) {
  // Performance-optimized scene setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(
    50, 
    container.clientWidth / container.clientHeight, 
    0.1, 
    1000
  );
  
  // Optimized renderer settings
  const renderer = new THREE.WebGLRenderer({ 
    antialias: !deviceCapabilities.capabilities.mobile,
    alpha: true,
    powerPreference: deviceCapabilities.capabilities.mobile ? 'low-power' : 'high-performance',
    stencil: false
  });
  
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, deviceCapabilities.capabilities.mobile ? 1.5 : 2));
  renderer.setClearColor(0x000000, 0);
  container.appendChild(renderer.domElement);

  // Create button group
  const buttonGroup = new THREE.Group();
  scene.add(buttonGroup);

  // Optimized geometry based on device capabilities
  const buttonDetail = deviceCapabilities.capabilities.mobile ? 16 : 32;
  const particleCount = deviceCapabilities.capabilities.mobile ? 10 : 20;

  // Main button with optimized geometry
  const buttonGeometry = new THREE.CylinderGeometry(2, 2, 0.5, buttonDetail);
  const buttonMaterial = new THREE.MeshPhongMaterial({
    color: 0x0056b3,
    shininess: deviceCapabilities.capabilities.mobile ? 50 : 100,
    transparent: true,
    opacity: 0.9
  });
  const button = new THREE.Mesh(buttonGeometry, buttonMaterial);
  buttonGroup.add(button);

  // Button ring with optimized geometry
  const ringGeometry = new THREE.TorusGeometry(2.2, 0.1, 8, buttonDetail);
  const ringMaterial = new THREE.MeshPhongMaterial({
    color: 0xFFD700,
    shininess: deviceCapabilities.capabilities.mobile ? 50 : 100
  });
  const ring = new THREE.Mesh(ringGeometry, ringMaterial);
  buttonGroup.add(ring);

  // Floating particles with reduced count on mobile
  const particles = [];
  const particleGeometry = new THREE.SphereGeometry(0.05, 6, 6);
  const particleMaterial = new THREE.MeshBasicMaterial({
    color: 0xFFD700,
    transparent: true,
    opacity: 0.8
  });

  for (let i = 0; i < particleCount; i++) {
    const particle = new THREE.Mesh(particleGeometry, particleMaterial);
    const angle = (i / particleCount) * Math.PI * 2;
    const radius = 3 + Math.random() * 2;
    
    particle.position.x = Math.cos(angle) * radius;
    particle.position.z = Math.sin(angle) * radius;
    particle.position.y = (Math.random() - 0.5) * 2;
    
    particle.userData = {
      originalY: particle.position.y,
      speed: 0.01 + Math.random() * 0.02,
      amplitude: 0.5 + Math.random() * 0.5
    };
    
    buttonGroup.add(particle);
    particles.push(particle);
  }

  // Optimized lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
  directionalLight.position.set(5, 5, 5);
  directionalLight.castShadow = false; // Disable shadows for performance
  scene.add(directionalLight);

  // Conditional point light (desktop only)
  let pointLight;
  if (!deviceCapabilities.capabilities.mobile) {
    pointLight = new THREE.PointLight(0xFFD700, 0.5, 10);
    pointLight.position.set(0, 3, 0);
    scene.add(pointLight);
  }

  // Camera positioning
  camera.position.set(0, 2, deviceCapabilities.capabilities.mobile ? 6 : 8);
  camera.lookAt(0, 0, 0);

  // Animation variables
  let time = 0;
  let isHovered = false;
  let isVisible = true;

  // Mouse interaction with optimized raycasting
  const mouse = new THREE.Vector2();
  const raycaster = new THREE.Raycaster();

  // Throttled mouse move handler
  let mouseMoveTimeout;
  container.addEventListener('mousemove', (event) => {
    clearTimeout(mouseMoveTimeout);
    mouseMoveTimeout = setTimeout(() => {
      const rect = container.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;

      raycaster.setFromCamera(mouse, camera);
      const intersects = raycaster.intersectObject(button);
      
      if (intersects.length > 0 && !isHovered) {
        isHovered = true;
        onButtonHover();
      } else if (intersects.length === 0 && isHovered) {
        isHovered = false;
        onButtonLeave();
      }
    }, 16); // ~60fps throttling
  });

  container.addEventListener('click', (event) => {
    const rect = container.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObject(button);
    
    if (intersects.length > 0) {
      onButtonClick();
    }
  });

  // Animation functions with fallback for missing GSAP
  function animateProperty(object, property, targetValue, duration = 0.3, onComplete = null) {
    if (typeof gsap !== 'undefined') {
      // Use GSAP if available
      return gsap.to(object[property], { 
        duration, 
        ...targetValue, 
        onComplete 
      });
    } else {
      // Fallback animation using requestAnimationFrame
      return animateWithRAF(object, property, targetValue, duration * 1000, onComplete);
    }
  }

  function animateWithRAF(object, property, targetValue, duration, onComplete) {
    const startTime = Date.now();
    const startValues = {};
    
    // Store initial values
    Object.keys(targetValue).forEach(key => {
      startValues[key] = object[property][key];
    });

    function animate() {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function (ease-out)
      const eased = 1 - Math.pow(1 - progress, 3);
      
      Object.keys(targetValue).forEach(key => {
        const start = startValues[key];
        const target = targetValue[key];
        object[property][key] = start + (target - start) * eased;
      });
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else if (onComplete) {
        onComplete();
      }
    }
    
    animate();
  }

  function onButtonHover() {
    // Animate button on hover
    animateProperty(button, 'scale', { x: 1.1, y: 1.1, z: 1.1 });
    animateProperty(buttonMaterial, '', { opacity: 1 });
    
    // Animate particles
    particles.forEach((particle, i) => {
      setTimeout(() => {
        animateProperty(particle, 'position', {
          y: particle.userData.originalY + particle.userData.amplitude
        }, 0.5);
      }, i * 20);
    });
  }

  function onButtonLeave() {
    // Reset button
    animateProperty(button, 'scale', { x: 1, y: 1, z: 1 });
    animateProperty(buttonMaterial, '', { opacity: 0.9 });
    
    // Reset particles
    particles.forEach((particle, i) => {
      setTimeout(() => {
        animateProperty(particle, 'position', {
          y: particle.userData.originalY
        }, 0.5);
      }, i * 20);
    });
  }

  function onButtonClick() {
    // Button press animation
    animateProperty(button, 'scale', { x: 0.95, y: 0.8, z: 0.95 }, 0.1, () => {
      animateProperty(button, 'scale', { x: 1.1, y: 1.1, z: 1.1 }, 0.2);
    });
    
    // Create explosion effect
    createExplosionEffect();
    
    // Trigger application action
    handleApplyAction();
  }

  function createExplosionEffect() {
    const explosionParticles = [];
    const explosionGeometry = new THREE.SphereGeometry(0.1, 6, 6);
    const explosionMaterial = new THREE.MeshBasicMaterial({
      color: 0xFFD700,
      transparent: true
    });

    const explosionCount = deviceCapabilities.capabilities.mobile ? 8 : 15;
    for (let i = 0; i < explosionCount; i++) {
      const particle = new THREE.Mesh(explosionGeometry, explosionMaterial);
      particle.position.copy(button.position);
      
      const direction = new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2
      ).normalize();
      
      particle.userData.velocity = direction.multiplyScalar(0.2);
      
      buttonGroup.add(particle);
      explosionParticles.push(particle);
    }

    // Animate explosion particles
    explosionParticles.forEach(particle => {
      const targetPos = {
        x: particle.position.x + particle.userData.velocity.x * 10,
        y: particle.position.y + particle.userData.velocity.y * 10,
        z: particle.position.z + particle.userData.velocity.z * 10
      };
      
      animateProperty(particle, 'position', targetPos, 1);
      animateProperty(particle.material, '', { opacity: 0 }, 1, () => {
        buttonGroup.remove(particle);
        particle.geometry.dispose();
        particle.material.dispose();
      });
    });
  }

  // Performance monitoring
  let frameCount = 0;
  let lastFPSCheck = Date.now();
  let currentFPS = 60;

  // Optimized animation loop
  let animationId;
  function animate() {
    animationId = requestAnimationFrame(animate);
    
    if (!isVisible) return;
    
    // Performance monitoring
    frameCount++;
    const now = Date.now();
    if (now - lastFPSCheck >= 1000) {
      currentFPS = frameCount;
      frameCount = 0;
      lastFPSCheck = now;
    }
    
    time += 0.01;
    
    // Rotate button group
    buttonGroup.rotation.y += deviceCapabilities.capabilities.mobile ? 0.003 : 0.005;
    
    // Animate particles
    particles.forEach(particle => {
      particle.position.y = particle.userData.originalY + 
        Math.sin(time * particle.userData.speed * 10) * particle.userData.amplitude * 0.3;
      particle.rotation.x += particle.userData.speed;
      particle.rotation.y += particle.userData.speed;
    });
    
    // Pulse ring
    ring.scale.setScalar(1 + Math.sin(time * 3) * 0.1);
    
    renderer.render(scene, camera);
  }

  // Visibility API for performance
  const visibilityObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      isVisible = entry.isIntersecting;
    });
  }, { threshold: 0.1 });
  
  visibilityObserver.observe(container);

  animate();

  // Handle resize with debouncing
  let resizeTimeout;
  const handleResize = () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    }, 150);
  };

  window.addEventListener('resize', handleResize);

  // Add text overlay
  createButtonText(container);

  return {
    scene,
    camera,
    renderer,
    buttonGroup,
    handleResize,
    getCurrentFPS: () => currentFPS,
    destroy: () => {
      cancelAnimationFrame(animationId);
      window.removeEventListener('resize', handleResize);
      visibilityObserver.disconnect();
      clearTimeout(mouseMoveTimeout);

      // Dispose of Three.js objects
      buttonGeometry.dispose();
      ringGeometry.dispose();
      particleGeometry.dispose();
      buttonMaterial.dispose();
      ringMaterial.dispose();
      particleMaterial.dispose();

      renderer.dispose();
      if (container.contains(renderer.domElement)) {
        container.removeChild(renderer.domElement);
      }
    }
  };
}

// Helper functions
function createButtonText(container) {
  const textOverlay = document.createElement('div');
  textOverlay.className = 'button-text-overlay';
  textOverlay.innerHTML = `
    <h3>Ready to Start Your Medical Journey?</h3>
    <p>Apply now for MBBS at FEFU</p>
    <div class="button-cta">
      <span class="cta-text">APPLY NOW</span>
      <i class="fas fa-arrow-right" aria-hidden="true"></i>
    </div>
  `;

  container.appendChild(textOverlay);
}

function createFallbackButton(container) {
  container.innerHTML = `
    <div class="fallback-apply-button">
      <div class="apply-button-content">
        <h3>Ready to Start Your Medical Journey?</h3>
        <p>Apply now for MBBS at FEFU</p>
        <button class="apply-btn-fallback" onclick="handleApplyAction()">
          <span>APPLY NOW</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </button>
      </div>
    </div>
  `;

  // Add enhanced interaction for fallback button
  const button = container.querySelector('.apply-btn-fallback');
  if (button) {
    button.addEventListener('mouseenter', () => {
      button.style.transform = 'translateY(-2px) scale(1.05)';
      button.style.boxShadow = '0 8px 25px rgba(0, 86, 179, 0.3)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.transform = 'translateY(0) scale(1)';
      button.style.boxShadow = '0 4px 15px rgba(0, 86, 179, 0.2)';
    });

    button.addEventListener('click', (e) => {
      // Add click animation
      button.style.transform = 'translateY(1px) scale(0.98)';
      setTimeout(() => {
        button.style.transform = 'translateY(-2px) scale(1.05)';
      }, 150);
    });
  }

  return {
    type: 'fallback',
    container,
    destroy: () => {
      // Cleanup event listeners
      const button = container.querySelector('.apply-btn-fallback');
      if (button) {
        button.replaceWith(button.cloneNode(true));
      }
    }
  };
}

function handleApplyAction() {
  // Track application click
  if (typeof gtag !== 'undefined') {
    gtag('event', 'apply_button_click', {
      'event_category': 'conversion',
      'event_label': 'main_apply_button'
    });
  }

  // Scroll to application form
  const applicationForm = document.querySelector('.application-form');
  if (applicationForm) {
    applicationForm.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });

    // Focus on first input with delay
    setTimeout(() => {
      const firstInput = applicationForm.querySelector('input');
      if (firstInput) {
        firstInput.focus();
      }
    }, 1000);
  }

  // Show success message
  showApplyMessage();
}

function showApplyMessage() {
  const message = document.createElement('div');
  message.className = 'apply-message';
  message.setAttribute('role', 'alert');
  message.setAttribute('aria-live', 'polite');

  message.innerHTML = `
    <div class="message-content">
      <i class="fas fa-check-circle" aria-hidden="true"></i>
      <p>Great choice! Please fill out the enquiry form below.</p>
    </div>
  `;

  document.body.appendChild(message);

  // Animate in
  requestAnimationFrame(() => {
    message.classList.add('show');
  });

  // Auto remove
  setTimeout(() => {
    message.classList.remove('show');
    setTimeout(() => {
      if (message.parentNode) {
        message.remove();
      }
    }, 300);
  }, 3000);
}

// Make handleApplyAction globally available
window.handleApplyAction = handleApplyAction;
