/* Base styles */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap');

:root {
  /* Color System */
  --primary-color: #0056b3;
  --primary-dark: #0041a3;
  --primary-light: #1976d2;
  --secondary-color: #ffd700;
  --secondary-dark: #e6c200;
  --text-color: #333;
  --text-light: #666;
  --text-muted: #999;
  --light-bg: #f8f9fa;
  --white: #ffffff;
  --black: #000000;
  --error-color: #dc3545;
  --success-color: #28a745;
  --warning-color: #ffc107;

  /* Dark mode colors */
  --dark-bg: #1a1a1a;
  --dark-surface: #2d2d2d;
  --dark-text: #e0e0e0;
  --dark-text-secondary: #b0b0b0;

  /* Shadows */
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
  --shadow-focus: 0 0 0 3px rgba(0, 86, 179, 0.2);

  /* Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Typography */
  --font-family-primary: 'Open Sans', sans-serif;
  --font-family-heading: 'Roboto', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 2.5rem;
  --font-size-5xl: 3rem;

  /* Layout */
  --border-radius: 12px;
  --border-radius-sm: 6px;
  --border-radius-lg: 16px;
  --container-max-width: 1200px;
  --header-height: 80px;

  /* Z-index scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* Performance optimizations */
  --gpu-acceleration: translateZ(0);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--white);
  overflow-x: hidden;
}

/* Header */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-light);
  position: fixed;
  width: 100%;
  z-index: 1000;
  transition: var(--transition-smooth);
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: 'Roboto', sans-serif;
  text-decoration: none;
}

nav ul {
  display: flex;
  list-style: none;
}

nav ul li {
  margin-left: 2rem;
}

nav ul li a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 600;
  transition: var(--transition-smooth);
  position: relative;
  padding: 0.5rem 0;
}

nav ul li a:hover {
  color: var(--primary-color);
}

nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition-smooth);
}

nav ul li a:hover::after {
  width: 100%;
}

/* Mobile Navigation */
.nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--primary-color);
  cursor: pointer;
}

/* Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--white);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Hero section */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  padding: 0 5%;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero-content {
  width: 50%;
  z-index: 2;
  animation: slideInLeft 1s ease-out;
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.2;
}

.hero h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
  font-weight: 600;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: var(--text-color);
  max-width: 500px;
}

/* Hero highlights */
.hero-highlights {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
  font-weight: 600;
}

.highlight-item i {
  color: var(--primary-color);
  font-size: 1.2rem;
}

/* CTA Buttons */
.hero-cta {
  display: flex;
  gap: 1rem;
}

.cta-button {
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: var(--transition-smooth);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-button.primary {
  background: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow-medium);
}

.cta-button.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.cta-button.secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button.secondary:hover {
  background: var(--primary-color);
  color: var(--white);
}

/* Globe container */
.globe-container {
  width: 50%;
  height: 100%;
  position: relative;
}

.globe-container canvas {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.globe-info {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
  backdrop-filter: blur(10px);
}

/* Info popup for globe */
.info-popup {
  position: absolute;
  background: var(--primary-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  box-shadow: var(--shadow-heavy);
  z-index: 10;
  animation: popupFadeIn 0.5s ease-out;
  max-width: 300px;
  text-align: center;
}

.info-popup::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid var(--primary-color);
}

.info-popup.fade-out {
  animation: popupFadeOut 1s ease-out forwards;
}

/* Section Padding */
.section-padding {
  padding: 5rem 5% 3rem;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  text-align: center;
}

.section-description {
  font-size: 1.1rem;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  color: var(--text-light);
  text-align: center;
}

/* Sections */
.info-section,
.campus-section,
.why-fefu-section,
.apply-section,
.enquiry-section {
  padding: 5rem 5% 3rem;
  text-align: center;
}

.info-section h2,
.campus-section h2,
.why-fefu-section h2,
.apply-section h2,
.enquiry-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
}

.info-section p,
.campus-section p,
.why-fefu-section p,
.apply-section p,
.enquiry-section p {
  font-size: 1.1rem;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  color: var(--text-light);
}

/* 3D Info Cards */
.info-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  perspective: 1000px;
}

.info-card {
  height: 300px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  cursor: pointer;
}

.info-card:hover {
  transform: rotateY(180deg);
}

.info-card-front,
.info-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
}

.info-card-front {
  background: linear-gradient(135deg, var(--primary-color), #0041a3);
  color: white;
}

.info-card-back {
  background: white;
  color: var(--text-color);
  transform: rotateY(180deg);
  border: 2px solid var(--primary-color);
}

.info-card-front h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.info-card-front .card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.info-card-back h4 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-weight: 600;
}

.info-card-back p {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.info-card-back .highlight {
  color: var(--primary-color);
  font-weight: 600;
}

/* Campus Gallery */
.campus-gallery {
  height: 500px;
  position: relative;
  margin: 3rem auto;
  max-width: 1200px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
}

/* Component Loading */
.component-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-bg);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Advantages Container */
.advantages-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.advantage-item {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  transition: var(--transition-smooth);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.advantage-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.advantage-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s;
}

.advantage-item:hover::before {
  left: 100%;
}

.advantage-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  transition: var(--transition-smooth);
}

.advantage-item:hover .advantage-icon {
  background: var(--secondary-color);
  color: var(--primary-color);
  transform: scale(1.1);
}

.advantage-item h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-weight: 600;
}

.advantage-item p {
  font-size: 1rem;
  color: var(--text-light);
  line-height: 1.5;
}

/* Apply Button Container */
.apply-button-container {
  margin: 3rem auto;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Application Form */
.application-form {
  max-width: 600px;
  margin: 3rem auto;
  background: white;
  padding: 3rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.application-form h3 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: var(--primary-color);
  text-align: center;
  font-family: 'Roboto', sans-serif;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-smooth);
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: var(--error-color);
}

.form-group input.valid,
.form-group select.valid,
.form-group textarea.valid {
  border-color: var(--success-color);
}

.field-error {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-top: 0.25rem;
}

.submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
}

.submit-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.submit-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

/* Form Messages */
.form-message {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-message.success {
  background: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.form-message.error {
  background: rgba(220, 53, 69, 0.1);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

/* Footer */
footer {
  background: var(--text-color);
  color: white;
  padding: 3rem 5% 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footer-section p {
  margin-bottom: 0.5rem;
  color: #ccc;
}

.footer-section a {
  color: #ccc;
  text-decoration: none;
  transition: var(--transition-smooth);
}

.footer-section a:hover {
  color: var(--secondary-color);
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icons a {
  color: white;
  font-size: 1.5rem;
  transition: var(--transition-smooth);
}

.social-icons a:hover {
  color: var(--secondary-color);
  transform: scale(1.2);
}

.copyright {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #555;
  color: #ccc;
}

/* Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes popupFadeOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-xl); }

.hidden { display: none; }
.visible { display: block; }

.fade-in { animation: fadeIn 0.6s ease-out; }
.slide-in-left { animation: slideInLeft 0.6s ease-out; }
.slide-in-right { animation: slideInRight 0.6s ease-out; }
.slide-in-up { animation: slideInUp 0.6s ease-out; }

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero h1 {
    font-size: 3rem;
  }

  .hero h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 992px) {
  .hero {
    flex-direction: column;
    text-align: center;
    padding: 2rem 5%;
  }

  .hero-content,
  .globe-container {
    width: 100%;
  }

  .hero-content {
    margin-bottom: 2rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .hero-highlights {
    justify-content: center;
    flex-wrap: wrap;
  }

  .info-cards-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .advantages-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  header {
    padding: 1rem 3%;
  }

  .nav-toggle {
    display: block;
  }

  nav ul {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: white;
    flex-direction: column;
    box-shadow: var(--shadow-medium);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }

  nav ul.active {
    display: flex;
  }

  nav ul li {
    margin: 0;
    border-bottom: 1px solid var(--light-bg);
  }

  nav ul li:last-child {
    border-bottom: none;
  }

  nav ul li a {
    display: block;
    padding: 1rem;
    text-align: center;
  }

  .hero {
    padding: 1rem 3%;
    min-height: 100vh;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero h2 {
    font-size: 1.5rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .info-section,
  .campus-section,
  .why-fefu-section,
  .apply-section,
  .enquiry-section {
    padding: 3rem 3% 2rem;
  }

  .application-form {
    margin: 2rem 1rem;
    padding: 2rem 1.5rem;
  }

  .info-cards-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .advantages-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 1.8rem;
  }

  .hero h2 {
    font-size: 1.3rem;
  }

  .hero-highlights {
    flex-direction: column;
    gap: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .application-form {
    padding: 1.5rem 1rem;
  }

  .info-card {
    height: 250px;
  }

  .info-card-front,
  .info-card-back {
    padding: 1.5rem;
  }

  .advantage-item {
    padding: 1.5rem;
  }
}

/* Print Styles */
@media print {
  header,
  .hero-cta,
  .apply-button-container,
  .application-form,
  footer {
    display: none;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .hero {
    height: auto;
    page-break-after: always;
  }

  .info-section,
  .campus-section,
  .why-fefu-section {
    page-break-inside: avoid;
  }
}
