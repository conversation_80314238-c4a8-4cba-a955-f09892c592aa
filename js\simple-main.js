// Simplified main script for MBBS Vostrix - guaranteed to work
import { createOptimizedGlobe } from './optimized-globe.js';
import { createInfoCards } from './infoCards.js';
import { createAdvantages } from './advantages.js';
import { createSimpleCampusGallery, createSimpleApplyButton, injectSimpleStyles } from './simple-components.js';

class SimpleMBBSVostrixApp {
  constructor() {
    this.components = new Map();
    this.isLoaded = false;
    this.init();
  }

  async init() {
    console.log('🚀 Starting SimpleMBBSVostrixApp...');
    
    try {
      // Inject styles for simple components
      injectSimpleStyles();
      
      // Show loading state
      this.showLoadingState();
      
      // Initialize critical components first
      await this.initializeCriticalComponents();
      
      // Initialize non-critical components
      await this.initializeNonCriticalComponents();
      
      // Force remove any remaining loading states
      this.forceRemoveLoadingStates();
      
      // Hide global loading state
      this.hideLoadingState();
      
      this.isLoaded = true;
      console.log('✅ SimpleMBBSVostrixApp initialized successfully');
      
    } catch (error) {
      console.error('❌ App initialization failed:', error);
      this.forceRemoveLoadingStates();
      this.showFallbackContent();
    }
  }

  showLoadingState() {
    console.log('Showing loading state...');
  }

  hideLoadingState() {
    const loader = document.getElementById('global-loader');
    if (loader) {
      loader.style.opacity = '0';
      setTimeout(() => loader.remove(), 500);
    }
    console.log('Global loading state hidden');
  }

  async initializeCriticalComponents() {
    console.log('Initializing critical components...');
    
    const criticalComponents = [
      { id: 'globe', container: 'globe-container', factory: createOptimizedGlobe },
      { id: 'infoCards', container: 'info-cards-container', factory: createInfoCards }
    ];

    for (const { id, container, factory } of criticalComponents) {
      const element = document.getElementById(container);
      if (element) {
        try {
          console.log(`Initializing ${id}...`);
          const component = await factory(element, this.getDeviceCapabilities());
          if (component) {
            this.components.set(id, component);
            console.log(`✅ ${id} initialized successfully`);
          } else {
            console.warn(`⚠️ ${id} returned null`);
          }
        } catch (error) {
          console.error(`❌ ${id} failed:`, error);
        }
      } else {
        console.warn(`⚠️ Container not found: ${container}`);
      }
    }
  }

  async initializeNonCriticalComponents() {
    console.log('Initializing non-critical components...');
    
    const nonCriticalComponents = [
      { id: 'campusGallery', container: 'campus-gallery', factory: createSimpleCampusGallery },
      { id: 'advantages', container: 'advantages-container', factory: createAdvantages },
      { id: 'applyButton', container: 'apply-button-container', factory: createSimpleApplyButton }
    ];

    for (const { id, container, factory } of nonCriticalComponents) {
      const element = document.getElementById(container);
      if (element) {
        try {
          console.log(`Initializing ${id}...`);
          
          // Remove loading state immediately
          this.removeLoadingState(element);
          
          const component = await factory(element, this.getDeviceCapabilities());
          if (component) {
            this.components.set(id, component);
            console.log(`✅ ${id} initialized successfully`);
          } else {
            console.warn(`⚠️ ${id} returned null`);
            this.showComponentFallback(element, id);
          }
        } catch (error) {
          console.error(`❌ ${id} failed:`, error);
          this.showComponentFallback(element, id);
        }
      } else {
        console.warn(`⚠️ Container not found: ${container}`);
      }
      
      // Small delay between components
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  removeLoadingState(container) {
    const loadingElement = container.querySelector('.component-loading');
    if (loadingElement) {
      console.log('Removing loading state from:', container.id);
      loadingElement.style.opacity = '0';
      setTimeout(() => {
        if (loadingElement.parentNode) {
          loadingElement.remove();
        }
      }, 300);
    }
    container.classList.remove('component-loading');
  }

  forceRemoveLoadingStates() {
    console.log('Force removing all loading states...');
    const allLoadingElements = document.querySelectorAll('.component-loading');
    allLoadingElements.forEach((element, index) => {
      console.log(`Removing loading element ${index + 1}:`, element);
      element.style.opacity = '0';
      setTimeout(() => {
        if (element.parentNode) {
          element.remove();
        }
      }, 300);
    });
  }

  showComponentFallback(container, componentId) {
    const fallbackContent = this.getFallbackContent(componentId);
    container.innerHTML = fallbackContent;
    container.classList.add('component-fallback');
    console.log(`Showing fallback for ${componentId}`);
  }

  getFallbackContent(componentId) {
    const fallbackMessages = {
      'campusGallery': `
        <div class="fallback-content">
          <i class="fas fa-images" style="font-size: 3rem; color: #0056b3; margin-bottom: 1rem;"></i>
          <h3>Campus Gallery</h3>
          <p>Campus gallery is loading...</p>
          <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #0056b3; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Refresh Page
          </button>
        </div>
      `,
      'advantages': `
        <div class="fallback-content">
          <i class="fas fa-star" style="font-size: 3rem; color: #0056b3; margin-bottom: 1rem;"></i>
          <h3>Why Choose FEFU?</h3>
          <ul style="text-align: left; max-width: 400px; margin: 1rem auto;">
            <li>WHO & NMC Recognized</li>
            <li>English Medium Education</li>
            <li>Affordable Fees</li>
            <li>Modern Facilities</li>
            <li>International Community</li>
          </ul>
        </div>
      `,
      'applyButton': `
        <div class="fallback-content">
          <i class="fas fa-graduation-cap" style="font-size: 3rem; color: #0056b3; margin-bottom: 1rem;"></i>
          <h3>Ready to Apply?</h3>
          <p>Start your medical journey at FEFU today!</p>
          <a href="#apply" style="display: inline-flex; align-items: center; gap: 0.5rem; margin-top: 1rem; padding: 1rem 2rem; background: #0056b3; color: white; text-decoration: none; border-radius: 4px;">
            Apply Now <i class="fas fa-arrow-right"></i>
          </a>
        </div>
      `
    };

    return fallbackMessages[componentId] || `
      <div class="fallback-content">
        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ffc107; margin-bottom: 1rem;"></i>
        <h3>Component Unavailable</h3>
        <p>This component is temporarily unavailable.</p>
        <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #ffc107; color: #000; border: none; border-radius: 4px; cursor: pointer;">
          Refresh Page
        </button>
      </div>
    `;
  }

  showFallbackContent() {
    console.log('Showing fallback content for entire app');
    // Show fallback for any components that failed
    const containers = ['campus-gallery', 'advantages-container', 'apply-button-container'];
    containers.forEach(containerId => {
      const container = document.getElementById(containerId);
      if (container && container.querySelector('.component-loading')) {
        this.removeLoadingState(container);
        this.showComponentFallback(container, containerId.replace('-container', '').replace('-', ''));
      }
    });
  }

  getDeviceCapabilities() {
    return {
      capabilities: {
        mobile: window.innerWidth < 768,
        webgl: !!window.WebGLRenderingContext,
        performance: 'medium'
      },
      shouldUse3D: () => false // Force simple components for now
    };
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing SimpleMBBSVostrixApp...');
  window.simpleMbbsVostrixApp = new SimpleMBBSVostrixApp();
});

// Export for module usage
export default SimpleMBBSVostrixApp;
