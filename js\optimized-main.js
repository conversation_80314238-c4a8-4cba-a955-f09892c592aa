import { createOptimizedGlobe } from './optimized-globe.js';
import { createInfoCards } from './infoCards.js';
import { createCampusGallery } from './campusGallery.js';
import { createAdvantages } from './advantages.js';
import { createApplyButton } from './applyButton.js';

// Performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.observers = new Map();
  }

  startTiming(name) {
    this.metrics[name] = { start: performance.now() };
  }

  endTiming(name) {
    if (this.metrics[name]) {
      this.metrics[name].duration = performance.now() - this.metrics[name].start;
      console.log(`${name}: ${this.metrics[name].duration.toFixed(2)}ms`);
    }
  }

  observeIntersection(element, callback, options = {}) {
    const observer = new IntersectionObserver(callback, {
      threshold: 0.1,
      rootMargin: '50px',
      ...options
    });
    observer.observe(element);
    this.observers.set(element, observer);
    return observer;
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

// Enhanced error boundary
class ErrorBoundary {
  constructor() {
    this.errors = [];
    this.setupGlobalErrorHandling();
  }

  setupGlobalErrorHandling() {
    window.addEventListener('error', (event) => {
      this.handleError(event.error, 'Global Error');
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, 'Unhandled Promise Rejection');
    });
  }

  handleError(error, context = 'Unknown') {
    const errorInfo = {
      message: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace',
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.errors.push(errorInfo);
    console.error(`[${context}]`, error);

    // Send to analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', 'exception', {
        description: errorInfo.message,
        fatal: false
      });
    }

    return errorInfo;
  }

  async safeExecute(fn, fallback = null, context = 'Safe Execute') {
    try {
      return await fn();
    } catch (error) {
      this.handleError(error, context);
      return fallback;
    }
  }
}

// Device capability detection
class DeviceCapabilities {
  constructor() {
    this.capabilities = this.detectCapabilities();
  }

  detectCapabilities() {
    return {
      webgl: this.hasWebGL(),
      webgl2: this.hasWebGL2(),
      touchDevice: 'ontouchstart' in window,
      highDPI: window.devicePixelRatio > 1,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      darkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
      mobile: window.innerWidth <= 768,
      tablet: window.innerWidth > 768 && window.innerWidth <= 1024,
      desktop: window.innerWidth > 1024,
      memoryLimit: this.getMemoryInfo(),
      connectionSpeed: this.getConnectionSpeed()
    };
  }

  hasWebGL() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  hasWebGL2() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  getMemoryInfo() {
    if ('memory' in performance) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  getConnectionSpeed() {
    if ('connection' in navigator) {
      return {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      };
    }
    return null;
  }

  shouldUse3D() {
    return this.capabilities.webgl && 
           !this.capabilities.reducedMotion && 
           !this.isLowEndDevice();
  }

  isLowEndDevice() {
    const memory = this.capabilities.memoryLimit;
    const connection = this.capabilities.connectionSpeed;
    
    return (memory && memory.limit < 1000000000) || // Less than 1GB
           (connection && connection.effectiveType === 'slow-2g') ||
           (this.capabilities.mobile && window.devicePixelRatio < 2);
  }
}

// Main application class with optimizations
class OptimizedMBBSVostrixApp {
  constructor() {
    this.components = new Map();
    this.isLoaded = false;
    this.performanceMonitor = new PerformanceMonitor();
    this.errorBoundary = new ErrorBoundary();
    this.deviceCapabilities = new DeviceCapabilities();
    this.loadingQueue = [];
    this.intersectionObserver = null;
    
    this.init();
  }

  async init() {
    this.performanceMonitor.startTiming('App Initialization');
    
    try {
      // Add skip link for accessibility
      this.addSkipLink();
      
      // Show loading state
      this.showLoadingState();
      
      // Setup theme detection
      this.setupThemeDetection();
      
      // Initialize critical components first
      await this.initializeCriticalComponents();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Setup intersection observer for lazy loading
      this.setupLazyLoading();
      
      // Initialize non-critical components
      await this.initializeNonCriticalComponents();

      // Ensure all loading states are removed (fallback)
      this.ensureLoadingStatesRemoved();

      // Hide loading state
      this.hideLoadingState();
      
      this.isLoaded = true;
      this.performanceMonitor.endTiming('App Initialization');
      
      // Setup performance monitoring
      this.setupPerformanceMonitoring();
      
    } catch (error) {
      this.errorBoundary.handleError(error, 'App Initialization');
      this.showFallbackContent();
    }
  }

  addSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link';
    skipLink.textContent = 'Skip to main content';
    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  setupThemeDetection() {
    // Detect system theme preference
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const updateTheme = (e) => {
      document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
    };
    
    updateTheme(darkModeQuery);
    darkModeQuery.addEventListener('change', updateTheme);
  }

  async initializeCriticalComponents() {
    // Initialize components that are immediately visible
    const criticalComponents = [
      { id: 'globe', container: 'globe-container', factory: createOptimizedGlobe },
      { id: 'infoCards', container: 'info-cards-container', factory: createInfoCards }
    ];

    for (const { id, container, factory } of criticalComponents) {
      const element = document.getElementById(container);
      if (element) {
        this.performanceMonitor.startTiming(`${id} initialization`);
        
        const component = await this.errorBoundary.safeExecute(
          () => factory(element, this.deviceCapabilities),
          null,
          `${id} initialization`
        );
        
        if (component) {
          this.components.set(id, component);
        }
        
        this.performanceMonitor.endTiming(`${id} initialization`);
      }
    }
  }

  async initializeNonCriticalComponents() {
    // Initialize components that can be loaded later
    const nonCriticalComponents = [
      { id: 'campusGallery', container: 'campus-gallery', factory: createCampusGallery },
      { id: 'advantages', container: 'advantages-container', factory: createAdvantages },
      { id: 'applyButton', container: 'apply-button-container', factory: createApplyButton }
    ];

    // Load components with delay to avoid blocking
    for (const { id, container, factory } of nonCriticalComponents) {
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay

      const element = document.getElementById(container);
      if (element) {
        console.log(`Initializing ${id} component...`);

        // Set timeout fallback to remove loading state
        const fallbackTimeout = setTimeout(() => {
          console.warn(`Component ${id} took too long to load, removing loading state`);
          this.removeLoadingState(element);
          this.showComponentFallback(element, id);
        }, 5000);

        try {
          // Remove loading state immediately
          this.removeLoadingState(element);

          const component = await this.errorBoundary.safeExecute(
            () => factory(element, this.deviceCapabilities),
            null,
            `${id} initialization`
          );

          clearTimeout(fallbackTimeout);

          if (component) {
            this.components.set(id, component);
            console.log(`✓ ${id} component initialized successfully`);
          } else {
            console.warn(`✗ ${id} component failed to initialize`);
            this.showComponentFallback(element, id);
          }
        } catch (error) {
          clearTimeout(fallbackTimeout);
          console.error(`Error initializing ${id}:`, error);
          this.showComponentFallback(element, id);
        }
      } else {
        console.warn(`Container not found for ${id}: ${container}`);
      }
    }
  }

  setupLazyLoading() {
    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target;
          const componentId = element.dataset.component;
          
          if (componentId && !this.components.has(componentId)) {
            this.loadComponent(componentId, element);
          }
          
          // Trigger animations
          element.classList.add('animate-in');
          this.intersectionObserver.unobserve(element);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });

    // Observe all sections
    document.querySelectorAll('section[data-component]').forEach(section => {
      this.intersectionObserver.observe(section);
    });
  }

  async loadComponent(componentId, element) {
    const componentFactories = {
      'campus-gallery': createCampusGallery,
      'advantages': createAdvantages,
      'apply-button': createApplyButton
    };

    const factory = componentFactories[componentId];
    if (factory) {
      // Find the actual container element
      const container = element.querySelector(`#${componentId.replace('-', '-')}-container`) ||
                      element.querySelector('.advantages-container') ||
                      element.querySelector('.campus-gallery') ||
                      element.querySelector('.apply-button-container');

      if (container) {
        // Remove loading state
        this.removeLoadingState(container);

        const component = await this.errorBoundary.safeExecute(
          () => factory(container, this.deviceCapabilities),
          null,
          `Lazy load ${componentId}`
        );

        if (component) {
          this.components.set(componentId, component);
        } else {
          // Show fallback content if component failed to load
          this.showComponentFallback(container, componentId);
        }
      }
    }
  }

  setupEventListeners() {
    // Optimized resize handler with debouncing
    let resizeTimeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        this.handleResize();
      }, 150);
    };
    
    window.addEventListener('resize', handleResize, { passive: true });
    
    // Form submission with validation
    const enquiryForm = document.getElementById('enquiry-form');
    if (enquiryForm) {
      enquiryForm.addEventListener('submit', this.handleFormSubmit.bind(this));
    }
    
    // Smooth scrolling for navigation
    this.setupSmoothScrolling();
    
    // Mobile navigation
    this.setupMobileNavigation();
    
    // Keyboard navigation
    this.setupKeyboardNavigation();
  }

  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // ESC key to close modals
      if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal, .image-modal');
        modals.forEach(modal => modal.remove());
      }
      
      // Tab navigation for 3D components
      if (e.key === 'Tab') {
        this.handleTabNavigation(e);
      }
    });
  }

  handleTabNavigation(e) {
    const focusableElements = document.querySelectorAll(
      'a[href], button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    if (e.shiftKey && document.activeElement === firstElement) {
      e.preventDefault();
      lastElement.focus();
    } else if (!e.shiftKey && document.activeElement === lastElement) {
      e.preventDefault();
      firstElement.focus();
    }
  }

  setupSmoothScrolling() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          const headerHeight = document.querySelector('header').offsetHeight;
          const targetPosition = targetElement.offsetTop - headerHeight;
          
          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
          
          // Update focus for accessibility
          targetElement.setAttribute('tabindex', '-1');
          targetElement.focus();
        }
      });
    });
  }

  setupMobileNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const nav = document.querySelector('nav');
    
    if (navToggle && nav) {
      navToggle.addEventListener('click', () => {
        nav.classList.toggle('nav-open');
        navToggle.classList.toggle('active');
        
        // Update ARIA attributes
        const isOpen = nav.classList.contains('nav-open');
        navToggle.setAttribute('aria-expanded', isOpen);
        nav.setAttribute('aria-hidden', !isOpen);
      });
    }
  }

  handleResize() {
    const newCapabilities = this.deviceCapabilities.detectCapabilities();
    const wasMobile = this.deviceCapabilities.capabilities.mobile;
    const isMobile = newCapabilities.mobile;
    
    this.deviceCapabilities.capabilities = newCapabilities;
    
    // Reinitialize components if device type changed
    if (wasMobile !== isMobile) {
      this.reinitializeComponents();
    }
    
    // Update component sizes
    this.components.forEach((component, id) => {
      if (component && typeof component.handleResize === 'function') {
        component.handleResize();
      }
    });
  }

  async reinitializeComponents() {
    // Dispose of existing components
    this.components.forEach((component, id) => {
      if (component && typeof component.destroy === 'function') {
        component.destroy();
      }
    });
    
    this.components.clear();
    
    // Reinitialize with new capabilities
    await this.initializeCriticalComponents();
    await this.initializeNonCriticalComponents();
  }

  async handleFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('.submit-btn');
    
    // Validate form
    if (!this.validateForm(form)) {
      return;
    }
    
    // Show loading state
    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;
    
    try {
      await this.submitForm(formData);
      this.showFormSuccess();
      form.reset();
    } catch (error) {
      this.errorBoundary.handleError(error, 'Form Submission');
      this.showFormError();
    } finally {
      submitBtn.textContent = 'Submit Enquiry';
      submitBtn.disabled = false;
    }
  }

  validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, 'This field is required');
        isValid = false;
      } else {
        this.clearFieldError(field);
      }
    });
    
    // Email validation
    const emailField = form.querySelector('input[type="email"]');
    if (emailField && emailField.value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailField.value)) {
        this.showFieldError(emailField, 'Please enter a valid email address');
        isValid = false;
      }
    }
    
    return isValid;
  }

  showFieldError(field, message) {
    this.clearFieldError(field);
    
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.setAttribute('role', 'alert');
    
    field.parentNode.appendChild(errorElement);
    field.classList.add('error');
    field.setAttribute('aria-invalid', 'true');
  }

  clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }
    field.classList.remove('error');
    field.removeAttribute('aria-invalid');
  }

  async submitForm(formData) {
    // Simulate API call - replace with actual endpoint
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90% success rate for demo
          resolve();
        } else {
          reject(new Error('Network error'));
        }
      }, 1000);
    });
  }

  showFormSuccess() {
    this.showMessage('Thank you! Your enquiry has been submitted successfully.', 'success');
  }

  showFormError() {
    this.showMessage('Sorry, there was an error submitting your enquiry. Please try again.', 'error');
  }

  showMessage(text, type = 'info') {
    const message = document.createElement('div');
    message.className = `form-message ${type}`;
    message.textContent = text;
    message.setAttribute('role', 'alert');
    
    const form = document.getElementById('enquiry-form');
    form.parentNode.insertBefore(message, form);
    
    setTimeout(() => {
      message.classList.add('fade-out');
      setTimeout(() => message.remove(), 300);
    }, 5000);
  }

  showLoadingState() {
    const loader = document.createElement('div');
    loader.id = 'global-loader';
    loader.className = 'global-loader';
    loader.innerHTML = `
      <div class="loader-content">
        <div class="loading-spinner"></div>
        <p>Loading MBBS Vostrix...</p>
      </div>
    `;
    loader.setAttribute('aria-label', 'Loading website content');
    document.body.appendChild(loader);
  }

  hideLoadingState() {
    const loader = document.getElementById('global-loader');
    if (loader) {
      loader.classList.add('fade-out');
      setTimeout(() => loader.remove(), 500);
    }
  }

  removeLoadingState(container) {
    // Remove loading spinner
    const loadingElement = container.querySelector('.component-loading');
    if (loadingElement) {
      loadingElement.style.opacity = '0';
      setTimeout(() => {
        if (loadingElement.parentNode) {
          loadingElement.remove();
        }
      }, 300);
    }

    // Remove loading class
    container.classList.remove('component-loading');
  }

  ensureLoadingStatesRemoved() {
    // Fallback method to remove any remaining loading states
    const allLoadingElements = document.querySelectorAll('.component-loading');
    allLoadingElements.forEach(element => {
      console.log('Removing stuck loading element:', element);
      element.style.opacity = '0';
      setTimeout(() => {
        if (element.parentNode) {
          element.remove();
        }
      }, 300);
    });

    // Also remove loading classes from containers
    const loadingContainers = document.querySelectorAll('.component-loading');
    loadingContainers.forEach(container => {
      container.classList.remove('component-loading');
    });
  }

  showComponentFallback(container, componentId) {
    const fallbackContent = this.getFallbackContent(componentId);
    container.innerHTML = fallbackContent;
    container.classList.add('component-fallback');
  }

  getFallbackContent(componentId) {
    const fallbackMessages = {
      'campusGallery': `
        <div class="fallback-content">
          <i class="fas fa-images" style="font-size: 3rem; color: #0056b3; margin-bottom: 1rem;"></i>
          <h3>Campus Gallery</h3>
          <p>Interactive campus gallery is not available on this device.</p>
          <p>Please visit our <a href="#campus" style="color: #0056b3;">campus features</a> section above for more information.</p>
        </div>
      `,
      'advantages': `
        <div class="fallback-content">
          <i class="fas fa-star" style="font-size: 3rem; color: #0056b3; margin-bottom: 1rem;"></i>
          <h3>Why Choose FEFU?</h3>
          <p>Interactive advantages showcase is not available on this device.</p>
          <ul style="text-align: left; max-width: 400px; margin: 1rem auto;">
            <li>WHO & NMC Recognized</li>
            <li>English Medium Education</li>
            <li>Affordable Fees</li>
            <li>Modern Facilities</li>
            <li>International Community</li>
          </ul>
        </div>
      `,
      'applyButton': `
        <div class="fallback-content">
          <i class="fas fa-graduation-cap" style="font-size: 3rem; color: #0056b3; margin-bottom: 1rem;"></i>
          <h3>Ready to Apply?</h3>
          <p>Start your medical journey at FEFU today!</p>
          <a href="#apply" class="cta-button primary" style="display: inline-flex; align-items: center; gap: 0.5rem; margin-top: 1rem;">
            Apply Now <i class="fas fa-arrow-right"></i>
          </a>
        </div>
      `
    };

    return fallbackMessages[componentId] || `
      <div class="fallback-content">
        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ffc107; margin-bottom: 1rem;"></i>
        <h3>Component Unavailable</h3>
        <p>This interactive component is not available on your device.</p>
      </div>
    `;
  }

  showFallbackContent() {
    console.log('Showing fallback content for unsupported devices');
    
    // Replace 3D containers with static alternatives
    const containers = ['globe-container', 'campus-gallery', 'apply-button-container'];
    
    containers.forEach(id => {
      const container = document.getElementById(id);
      if (container) {
        container.innerHTML = `
          <div class="fallback-content">
            <p>Interactive content not available on this device.</p>
            <p>Please visit on a desktop browser for the full experience.</p>
          </div>
        `;
      }
    });
  }

  setupPerformanceMonitoring() {
    // Monitor memory usage
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          console.warn('High memory usage detected');
          this.optimizeMemoryUsage();
        }
      }, 30000); // Check every 30 seconds
    }
    
    // Monitor frame rate
    let lastTime = performance.now();
    let frameCount = 0;
    
    const checkFrameRate = (currentTime) => {
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        const fps = frameCount;
        frameCount = 0;
        lastTime = currentTime;
        
        if (fps < 30) {
          console.warn('Low frame rate detected:', fps);
          this.optimizePerformance();
        }
      }
      
      requestAnimationFrame(checkFrameRate);
    };
    
    requestAnimationFrame(checkFrameRate);
  }

  optimizeMemoryUsage() {
    // Dispose of unused components
    this.components.forEach((component, id) => {
      if (component && typeof component.optimize === 'function') {
        component.optimize();
      }
    });
    
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  optimizePerformance() {
    // Reduce quality for better performance
    this.components.forEach((component, id) => {
      if (component && typeof component.reduceQuality === 'function') {
        component.reduceQuality();
      }
    });
  }

  // Public API
  getComponent(name) {
    return this.components.get(name);
  }

  isComponentLoaded(name) {
    return this.components.has(name);
  }

  destroy() {
    // Cleanup all components
    this.components.forEach((component, id) => {
      if (component && typeof component.destroy === 'function') {
        component.destroy();
      }
    });
    
    this.components.clear();
    this.performanceMonitor.cleanup();
    
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.mbbsVostrixApp = new OptimizedMBBSVostrixApp();
});

// Export for module usage
export default OptimizedMBBSVostrixApp;
