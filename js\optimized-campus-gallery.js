import * as THREE from 'three';

// Optimized campus gallery with performance enhancements
export function createOptimizedCampusGallery(container, deviceCapabilities) {
  // Campus images data with optimized URLs
  const campusImages = [
    {
      url: 'https://images.unsplash.com/photo-**********-701939374585?w=800&h=600&fit=crop&auto=format&q=80',
      title: 'FEFU Main Campus',
      description: 'Modern campus facilities on Russky Island'
    },
    {
      url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop&auto=format&q=80',
      title: 'Medical Faculty Building',
      description: 'State-of-the-art medical education facilities'
    },
    {
      url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop&auto=format&q=80',
      title: 'Student Dormitories',
      description: 'Comfortable accommodation for international students'
    },
    {
      url: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&h=600&fit=crop&auto=format&q=80',
      title: 'Laboratory Facilities',
      description: 'Advanced medical research and training labs'
    },
    {
      url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&auto=format&q=80',
      title: 'Library & Study Areas',
      description: 'Extensive medical library and study spaces'
    },
    {
      url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&auto=format&q=80',
      title: 'Campus Life',
      description: 'Vibrant student community and activities'
    }
  ];

  // Check device capabilities and use appropriate implementation
  if (!deviceCapabilities.shouldUse3D()) {
    return createFallbackGallery(container, campusImages);
  }

  try {
    return create3DGallery(container, campusImages, deviceCapabilities);
  } catch (error) {
    console.warn('3D gallery failed, falling back to 2D:', error);
    return createFallbackGallery(container, campusImages);
  }
}

async function create3DGallery(container, images, deviceCapabilities) {
  // Performance-optimized scene setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(
    75, 
    container.clientWidth / container.clientHeight, 
    0.1, 
    1000
  );
  
  // Optimized renderer settings based on device capabilities
  const renderer = new THREE.WebGLRenderer({ 
    antialias: !deviceCapabilities.capabilities.mobile,
    alpha: true,
    powerPreference: deviceCapabilities.capabilities.mobile ? 'low-power' : 'high-performance',
    stencil: false
  });
  
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, deviceCapabilities.capabilities.mobile ? 1.5 : 2));
  renderer.outputColorSpace = THREE.SRGBColorSpace;
  container.appendChild(renderer.domElement);

  // Create carousel with optimized geometry
  const carousel = new THREE.Group();
  scene.add(carousel);

  const radius = deviceCapabilities.capabilities.mobile ? 6 : 8;
  const imageWidth = deviceCapabilities.capabilities.mobile ? 4 : 6;
  const imageHeight = deviceCapabilities.capabilities.mobile ? 3 : 4;
  const panels = [];

  // Optimized texture loading with caching
  const textureCache = new Map();
  const textureLoader = new THREE.TextureLoader();
  
  // Load textures with error handling and create panels
  for (let i = 0; i < images.length; i++) {
    const angle = (i / images.length) * Math.PI * 2;
    
    try {
      const texture = await loadTextureWithFallback(textureLoader, images[i].url, textureCache);
      const panel = createOptimizedImagePanel(texture, imageWidth, imageHeight, images[i], deviceCapabilities);
      
      panel.position.x = Math.cos(angle) * radius;
      panel.position.z = Math.sin(angle) * radius;
      panel.rotation.y = -angle;
      
      carousel.add(panel);
      panels.push({ panel, data: images[i], angle });
      
    } catch (error) {
      console.warn(`Failed to load image ${i}:`, error);
      // Create placeholder panel
      const placeholderPanel = createPlaceholderPanel(imageWidth, imageHeight, images[i]);
      placeholderPanel.position.x = Math.cos(angle) * radius;
      placeholderPanel.position.z = Math.sin(angle) * radius;
      placeholderPanel.rotation.y = -angle;
      carousel.add(placeholderPanel);
      panels.push({ panel: placeholderPanel, data: images[i], angle });
    }
  }

  // Camera positioning
  camera.position.set(0, 2, deviceCapabilities.capabilities.mobile ? 10 : 12);
  camera.lookAt(0, 0, 0);

  // Optimized lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  directionalLight.castShadow = false; // Disable shadows for performance
  scene.add(directionalLight);

  // Animation variables
  let currentRotation = 0;
  let targetRotation = 0;
  let isAutoRotating = true;
  let autoRotateSpeed = deviceCapabilities.capabilities.mobile ? 0.003 : 0.005;
  let isVisible = true;

  // Optimized controls
  const controls = {
    isInteracting: false,
    startX: 0,
    currentX: 0
  };

  // Event listeners with performance optimization
  setupOptimizedControls(container, controls, deviceCapabilities, () => {
    isAutoRotating = false;
    setTimeout(() => { isAutoRotating = true; }, 3000);
  });

  // Intersection detection for clicks
  const raycaster = new THREE.Raycaster();
  const mouse = new THREE.Vector2();

  container.addEventListener('click', (event) => {
    if (controls.isInteracting) return;
    
    const rect = container.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObjects(panels.map(p => p.panel));

    if (intersects.length > 0) {
      const clickedPanel = intersects[0].object;
      const panelData = panels.find(p => p.panel === clickedPanel || p.panel.children.includes(clickedPanel));
      if (panelData) {
        showImageModal(panelData.data);
      }
    }
  });

  // Performance monitoring
  let frameCount = 0;
  let lastFPSCheck = Date.now();
  let currentFPS = 60;

  // Optimized animation loop
  let animationId;
  function animate() {
    animationId = requestAnimationFrame(animate);
    
    if (!isVisible) return;

    // Performance monitoring
    frameCount++;
    const now = Date.now();
    if (now - lastFPSCheck >= 1000) {
      currentFPS = frameCount;
      frameCount = 0;
      lastFPSCheck = now;
      
      // Adjust quality based on performance
      if (currentFPS < 30) {
        autoRotateSpeed = Math.max(autoRotateSpeed * 0.8, 0.001);
      }
    }

    // Auto rotation
    if (isAutoRotating) {
      targetRotation += autoRotateSpeed;
    }

    // Manual rotation
    if (controls.isInteracting) {
      const deltaX = controls.currentX - controls.startX;
      targetRotation = currentRotation + deltaX * 0.005;
    }

    // Smooth rotation with easing
    currentRotation += (targetRotation - currentRotation) * 0.05;
    carousel.rotation.y = currentRotation;

    renderer.render(scene, camera);
  }

  // Visibility API for performance
  const visibilityObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      isVisible = entry.isIntersecting;
    });
  }, { threshold: 0.1 });
  
  visibilityObserver.observe(container);

  animate();

  // Handle resize with debouncing
  let resizeTimeout;
  const handleResize = () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    }, 150);
  };

  window.addEventListener('resize', handleResize);

  // Navigation controls
  createGalleryNavigation(container, panels.length, (index) => {
    const targetAngle = -(index / panels.length) * Math.PI * 2;
    targetRotation = targetAngle;
    isAutoRotating = false;
    
    setTimeout(() => {
      isAutoRotating = true;
    }, 3000);
  });

  return {
    scene,
    camera,
    renderer,
    carousel,
    panels,
    handleResize,
    getCurrentFPS: () => currentFPS,
    destroy: () => {
      cancelAnimationFrame(animationId);
      window.removeEventListener('resize', handleResize);
      visibilityObserver.disconnect();

      // Dispose of Three.js objects
      panels.forEach(({ panel }) => {
        if (panel.geometry) panel.geometry.dispose();
        if (panel.material) {
          if (Array.isArray(panel.material)) {
            panel.material.forEach(mat => mat.dispose());
          } else {
            panel.material.dispose();
          }
        }
      });

      // Dispose of textures
      textureCache.forEach(texture => texture.dispose());
      textureCache.clear();

      renderer.dispose();
      if (container.contains(renderer.domElement)) {
        container.removeChild(renderer.domElement);
      }
    }
  };
}

// Helper functions
async function loadTextureWithFallback(loader, url, cache) {
  if (cache.has(url)) {
    return cache.get(url);
  }

  return new Promise((resolve, reject) => {
    loader.load(
      url,
      (texture) => {
        // Optimize texture settings
        texture.generateMipmaps = true;
        texture.minFilter = THREE.LinearMipmapLinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;

        cache.set(url, texture);
        resolve(texture);
      },
      undefined,
      reject
    );
  });
}

function createOptimizedImagePanel(texture, width, height, imageData, deviceCapabilities) {
  // Use lower geometry detail on mobile
  const segments = deviceCapabilities.capabilities.mobile ? 1 : 2;
  const geometry = new THREE.PlaneGeometry(width, height, segments, segments);

  const material = new THREE.MeshLambertMaterial({
    map: texture,
    transparent: false // Disable transparency for performance
  });

  const panel = new THREE.Mesh(geometry, material);

  // Add frame only on desktop for performance
  if (!deviceCapabilities.capabilities.mobile) {
    const frameGeometry = new THREE.PlaneGeometry(width + 0.2, height + 0.2);
    const frameMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    frame.position.z = -0.01;
    panel.add(frame);
  }

  panel.userData = imageData;
  return panel;
}

function createPlaceholderPanel(width, height, imageData) {
  const geometry = new THREE.PlaneGeometry(width, height);
  const material = new THREE.MeshLambertMaterial({
    color: 0xcccccc,
    transparent: false
  });

  const panel = new THREE.Mesh(geometry, material);
  panel.userData = imageData;
  return panel;
}

function setupOptimizedControls(container, controls, deviceCapabilities, onInteraction) {
  // Mouse events
  container.addEventListener('mousedown', (e) => {
    controls.isInteracting = true;
    controls.startX = e.clientX;
    controls.currentX = e.clientX;
    onInteraction();
  });

  container.addEventListener('mousemove', (e) => {
    if (controls.isInteracting) {
      controls.currentX = e.clientX;
    }
  });

  container.addEventListener('mouseup', () => {
    controls.isInteracting = false;
  });

  container.addEventListener('mouseleave', () => {
    controls.isInteracting = false;
  });

  // Touch events with passive listeners for performance
  container.addEventListener('touchstart', (e) => {
    controls.isInteracting = true;
    controls.startX = e.touches[0].clientX;
    controls.currentX = e.touches[0].clientX;
    onInteraction();
  }, { passive: true });

  container.addEventListener('touchmove', (e) => {
    if (controls.isInteracting) {
      controls.currentX = e.touches[0].clientX;
    }
  }, { passive: true });

  container.addEventListener('touchend', () => {
    controls.isInteracting = false;
  }, { passive: true });
}

function createGalleryNavigation(container, imageCount, onNavigate) {
  const nav = document.createElement('div');
  nav.className = 'gallery-navigation';
  nav.setAttribute('role', 'navigation');
  nav.setAttribute('aria-label', 'Gallery navigation');

  for (let i = 0; i < imageCount; i++) {
    const dot = document.createElement('button');
    dot.className = 'gallery-dot';
    dot.setAttribute('aria-label', `View image ${i + 1} of ${imageCount}`);
    dot.addEventListener('click', () => onNavigate(i));
    nav.appendChild(dot);
  }

  container.appendChild(nav);
}

function showImageModal(imageData) {
  const modal = document.createElement('div');
  modal.className = 'image-modal';
  modal.setAttribute('role', 'dialog');
  modal.setAttribute('aria-modal', 'true');
  modal.setAttribute('aria-labelledby', 'modal-title');

  modal.innerHTML = `
    <div class="modal-content">
      <button class="modal-close" aria-label="Close modal">&times;</button>
      <img src="${imageData.url}" alt="${imageData.title}" loading="lazy">
      <div class="modal-info">
        <h3 id="modal-title">${imageData.title}</h3>
        <p>${imageData.description}</p>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Focus management
  const closeBtn = modal.querySelector('.modal-close');
  closeBtn.focus();

  const closeModal = () => {
    modal.remove();
  };

  closeBtn.addEventListener('click', closeModal);
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeModal();
    }
  });

  // Keyboard navigation
  modal.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeModal();
    }
  });
}

function createFallbackGallery(container, images) {
  container.innerHTML = `
    <div class="fallback-gallery">
      <div class="gallery-grid">
        ${images.map((img, index) => `
          <div class="gallery-item" role="button" tabindex="0" aria-label="View ${img.title}">
            <img src="${img.url}" alt="${img.title}" loading="lazy">
            <div class="gallery-item-info">
              <h3>${img.title}</h3>
              <p>${img.description}</p>
            </div>
          </div>
        `).join('')}
      </div>
    </div>
  `;

  // Add click handlers for fallback gallery
  const galleryItems = container.querySelectorAll('.gallery-item');
  galleryItems.forEach((item, index) => {
    const clickHandler = () => showImageModal(images[index]);
    item.addEventListener('click', clickHandler);
    item.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        clickHandler();
      }
    });
  });

  return {
    type: 'fallback',
    container,
    destroy: () => {
      // Cleanup event listeners
      const galleryItems = container.querySelectorAll('.gallery-item');
      galleryItems.forEach(item => {
        item.replaceWith(item.cloneNode(true));
      });
    }
  };
}
